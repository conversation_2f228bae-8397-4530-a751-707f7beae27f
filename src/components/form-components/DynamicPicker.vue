<template>
  <van-cell 
    :title="config.label"
    :required="config.required"
    is-link
    :value="displayValue"
    @click="showPicker = true"
  />
  
  <van-popup 
    v-model:show="showPicker" 
    position="bottom"
  >
    <van-picker
      :columns="columns"
      :title="config.properties?.title || config.label"
      :confirm-button-text="config.properties?.confirmButtonText || '确认'"
      :cancel-button-text="config.properties?.cancelButtonText || '取消'"
      :show-toolbar="config.properties?.showToolbar !== false"
      @confirm="onConfirm"
      @cancel="showPicker = false"
    />
  </van-popup>
</template>

<script>
import { ref, computed } from 'vue'

export default {
  name: 'DynamicPicker',
  props: {
    config: {
      type: Object,
      required: true
    },
    componentId: {
      type: String,
      required: true
    },
    modelValue: {
      type: [String, Number],
      default: ''
    }
  },
  emits: ['update:modelValue'],
  setup(props, { emit }) {
    const showPicker = ref(false)
    
    // 获取选择器列配置
    const columns = computed(() => {
      return props.config.properties?.columns || []
    })
    
    // 计算显示值
    const displayValue = computed(() => {
      if (!props.modelValue) return '请选择'
      
      // 查找对应的显示文本
      const firstColumn = columns.value[0]
      if (firstColumn && firstColumn.values) {
        const option = firstColumn.values.find(item => item.value === props.modelValue)
        return option ? option.text : props.modelValue
      }
      
      return props.modelValue
    })
    
    // 确认选择
    const onConfirm = ({ selectedOptions }) => {
      if (selectedOptions && selectedOptions.length > 0) {
        const selectedValue = selectedOptions[0].value
        emit('update:modelValue', selectedValue)
      }
      showPicker.value = false
    }
    
    return {
      showPicker,
      columns,
      displayValue,
      onConfirm
    }
  }
}
</script>

<style scoped>
/* 选择器样式可以在这里自定义 */
</style>

nLog("开始")
require("TSLib")
thread = require("thread")
Server = require("httpServer")
configExample = require("configExample")
thread.create(Server.start, {
  callBack = function()
    -- line: [7, 10] id: 1
    nLog("协程结束了", 0)
  end,
  errorBack = function(r0_2)
    -- line: [11, 14] id: 2
    nLog("协程错误了:" .. r0_2, 0)
  end,
  catchBack = function(r0_3)
    -- line: [15, 20] id: 3
    local r2_3 = require("ts").json
    nLog("协程异常了\n" .. json.encode(r0_3), 0)
  end,
})
for i = 1, 20, 1 do
  mSleep(100)
  if ServerPort then
    break
  end
end

-- 设置页面配置（可以选择不同的配置示例）
local pageConfig = configExample.createGameConfig()  -- 使用游戏配置示例
-- local pageConfig = configExample.createTabsConfig()  -- 或使用标签页配置示例
-- local pageConfig = configExample.createSimpleFormConfig()  -- 或使用简单表单配置示例
setPageConfig(pageConfig)

option = {}
showWebUI({
  originx = option.originx,
  originy = option.originy,
  width = option.width,
  height = option.height,
  orient = option.orient,
  cornerRadius = option.cornerRadius,
  id = "WEBUI",
  url = "http://127.0.0.1:" .. ServerPort .. "/",
})
thread.waitAllThreadExit()
closeWebUI("WEBUI")
bool = writeFileString(userPath() .. "/res/config.json", 配置)
dialog(配置)

:host,
:root {
    --van-black: #000;
    --van-white: #fff;
    --van-gray-1: #f7f8fa;
    --van-gray-2: #f2f3f5;
    --van-gray-3: #ebedf0;
    --van-gray-4: #dcdee0;
    --van-gray-5: #c8c9cc;
    --van-gray-6: #969799;
    --van-gray-7: #646566;
    --van-gray-8: #323233;
    --van-red: #ee0a24;
    --van-blue: #1989fa;
    --van-orange: #ff976a;
    --van-orange-dark: #ed6a0c;
    --van-orange-light: #fffbe8;
    --van-green: #07c160;
    --van-gradient-red: linear-gradient(90deg, #ff6034, #ee0a24);
    --van-gradient-orange: linear-gradient(90deg, #ffd01e, #ff8917);
    --van-primary-color: var(--van-blue);
    --van-success-color: var(--van-green);
    --van-danger-color: var(--van-red);
    --van-warning-color: var(--van-orange);
    --van-text-color: var(--van-gray-8);
    --van-text-color-2: var(--van-gray-6);
    --van-text-color-3: var(--van-gray-5);
    --van-active-color: var(--van-gray-2);
    --van-active-opacity: 0.6;
    --van-disabled-opacity: 0.5;
    --van-background: var(--van-gray-1);
    --van-background-2: var(--van-white);
    --van-background-3: var(--van-white);
    --van-padding-base: 4px;
    --van-padding-xs: 8px;
    --van-padding-sm: 12px;
    --van-padding-md: 16px;
    --van-padding-lg: 24px;
    --van-padding-xl: 32px;
    --van-font-bold: 600;
    --van-font-size-xs: 10px;
    --van-font-size-sm: 12px;
    --van-font-size-md: 14px;
    --van-font-size-lg: 16px;
    --van-line-height-xs: 14px;
    --van-line-height-sm: 18px;
    --van-line-height-md: 20px;
    --van-line-height-lg: 22px;
    --van-base-font: -apple-system, BlinkMacSystemFont, "Helvetica Neue", Helvetica, Segoe UI, Arial, Roboto, "PingFang SC", "miui", "Hiragino Sans GB", "Microsoft Yahei", sans-serif;
    --van-price-font: avenir-heavy, "PingFang SC", helvetica neue, arial, sans-serif;
    --van-duration-base: 0.3s;
    --van-duration-fast: 0.2s;
    --van-ease-out: ease-out;
    --van-ease-in: ease-in;
    --van-border-color: var(--van-gray-3);
    --van-border-width: 1px;
    --van-radius-sm: 2px;
    --van-radius-md: 4px;
    --van-radius-lg: 8px;
    --van-radius-max: 999px;
}
.van-theme-dark {
    --van-text-color: #f5f5f5;
    --van-text-color-2: #707070;
    --van-text-color-3: #4d4d4d;
    --van-border-color: #3a3a3c;
    --van-active-color: #3a3a3c;
    --van-background: #000;
    --van-background-2: #1c1c1e;
    --van-background-3: #37363b;
}
html {
    -webkit-tap-highlight-color: transparent;
}
body {
    margin: 0;
    font-family: var(--van-base-font);
}
a {
    text-decoration: none;
}
button,
input,
textarea {
    color: inherit;
    font: inherit;
}
[class*="van-"]:focus,
a:focus,
button:focus,
input:focus,
textarea:focus {
    outline: none;
}
ol,
ul {
    margin: 0;
    padding: 0;
    list-style: none;
}
@keyframes van-slide-up-enter {
    0% {
        transform: translate3d(0, 100%, 0);
    }
}
@keyframes van-slide-up-leave {
    to {
        transform: translate3d(0, 100%, 0);
    }
}
@keyframes van-slide-down-enter {
    0% {
        transform: translate3d(0, -100%, 0);
    }
}
@keyframes van-slide-down-leave {
    to {
        transform: translate3d(0, -100%, 0);
    }
}
@keyframes van-slide-left-enter {
    0% {
        transform: translate3d(-100%, 0, 0);
    }
}
@keyframes van-slide-left-leave {
    to {
        transform: translate3d(-100%, 0, 0);
    }
}
@keyframes van-slide-right-enter {
    0% {
        transform: translate3d(100%, 0, 0);
    }
}
@keyframes van-slide-right-leave {
    to {
        transform: translate3d(100%, 0, 0);
    }
}
@keyframes van-fade-in {
    0% {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}
@keyframes van-fade-out {
    0% {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}
@keyframes van-rotate {
    0% {
        transform: rotate(0);
    }
    to {
        transform: rotate(1turn);
    }
}
.van-fade-enter-active {
    animation: var(--van-duration-base) van-fade-in both var(--van-ease-out);
}
.van-fade-leave-active {
    animation: var(--van-duration-base) van-fade-out both var(--van-ease-in);
}
.van-slide-up-enter-active {
    animation: van-slide-up-enter var(--van-duration-base) both var(--van-ease-out);
}
.van-slide-up-leave-active {
    animation: van-slide-up-leave var(--van-duration-base) both var(--van-ease-in);
}
.van-slide-down-enter-active {
    animation: van-slide-down-enter var(--van-duration-base) both var(--van-ease-out);
}
.van-slide-down-leave-active {
    animation: van-slide-down-leave var(--van-duration-base) both var(--van-ease-in);
}
.van-slide-left-enter-active {
    animation: van-slide-left-enter var(--van-duration-base) both var(--van-ease-out);
}
.van-slide-left-leave-active {
    animation: van-slide-left-leave var(--van-duration-base) both var(--van-ease-in);
}
.van-slide-right-enter-active {
    animation: van-slide-right-enter var(--van-duration-base) both var(--van-ease-out);
}
.van-slide-right-leave-active {
    animation: van-slide-right-leave var(--van-duration-base) both var(--van-ease-in);
}
.van-clearfix:after {
    display: table;
    clear: both;
    content: "";
}
.van-ellipsis {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
.van-multi-ellipsis--l2 {
    -webkit-line-clamp: 2;
}
.van-multi-ellipsis--l2,
.van-multi-ellipsis--l3 {
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    line-break: anywhere;
    -webkit-box-orient: vertical;
}
.van-multi-ellipsis--l3 {
    -webkit-line-clamp: 3;
}
.van-safe-area-top {
    padding-top: constant(safe-area-inset-top);
    padding-top: env(safe-area-inset-top);
}
.van-safe-area-bottom {
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
}
.van-haptics-feedback {
    cursor: pointer;
}
.van-haptics-feedback:active {
    opacity: var(--van-active-opacity);
}
[class*="van-hairline"]:after {
    position: absolute;
    box-sizing: border-box;
    content: " ";
    pointer-events: none;
    top: -50%;
    right: -50%;
    bottom: -50%;
    left: -50%;
    border: 0 solid var(--van-border-color);
    transform: scale(0.5);
}
.van-hairline,
.van-hairline--bottom,
.van-hairline--left,
.van-hairline--right,
.van-hairline--surround,
.van-hairline--top,
.van-hairline--top-bottom {
    position: relative;
}
.van-hairline--top:after {
    border-top-width: var(--van-border-width);
}
.van-hairline--left:after {
    border-left-width: var(--van-border-width);
}
.van-hairline--right:after {
    border-right-width: var(--van-border-width);
}
.van-hairline--bottom:after {
    border-bottom-width: var(--van-border-width);
}
.van-hairline--top-bottom:after,
.van-hairline-unset--top-bottom:after {
    border-width: var(--van-border-width) 0;
}
.van-hairline--surround:after {
    border-width: var(--van-border-width);
}
:host,
:root {
    --van-action-bar-background: var(--van-background-2);
    --van-action-bar-height: 50px;
}
.van-action-bar {
    position: fixed;
    right: 0;
    bottom: 0;
    left: 0;
    display: flex;
    align-items: center;
    box-sizing: content-box;
    height: var(--van-action-bar-height);
    background: var(--van-action-bar-background);
}
:host,
:root {
    --van-badge-size: 16px;
    --van-badge-color: var(--van-white);
    --van-badge-padding: 0 3px;
    --van-badge-font-size: var(--van-font-size-sm);
    --van-badge-font-weight: var(--van-font-bold);
    --van-badge-border-width: var(--van-border-width);
    --van-badge-background: var(--van-danger-color);
    --van-badge-dot-color: var(--van-danger-color);
    --van-badge-dot-size: 8px;
    --van-badge-font: -apple-system-font, helvetica neue, arial, sans-serif;
}
.van-badge {
    display: inline-block;
    box-sizing: border-box;
    min-width: var(--van-badge-size);
    padding: var(--van-badge-padding);
    color: var(--van-badge-color);
    font-weight: var(--van-badge-font-weight);
    font-size: var(--van-badge-font-size);
    font-family: var(--van-badge-font);
    line-height: 1.2;
    text-align: center;
    background: var(--van-badge-background);
    border: var(--van-badge-border-width) solid var(--van-background-2);
    border-radius: var(--van-radius-max);
}
.van-badge--fixed {
    position: absolute;
    transform-origin: 100%;
}
.van-badge--top-left {
    top: 0;
    left: 0;
    transform: translate(-50%, -50%);
}
.van-badge--top-right {
    top: 0;
    right: 0;
    transform: translate(50%, -50%);
}
.van-badge--bottom-left {
    bottom: 0;
    left: 0;
    transform: translate(-50%, 50%);
}
.van-badge--bottom-right {
    bottom: 0;
    right: 0;
    transform: translate(50%, 50%);
}
.van-badge--dot {
    width: var(--van-badge-dot-size);
    min-width: 0;
    height: var(--van-badge-dot-size);
    background: var(--van-badge-dot-color);
    border-radius: 100%;
    border: none;
    padding: 0;
}
.van-badge__wrapper,
.van-icon {
    position: relative;
    display: inline-block;
}
.van-icon {
    font: 14px/1 vant-icon;
    font: normal normal normal 14px/1 var(--van-icon-font-family, "vant-icon");
    font-size: inherit;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
}
.van-icon:before {
    display: inline-block;
}
.van-icon-arrow-double-left:before {
    content: "\e653";
}
.van-icon-arrow-double-right:before {
    content: "\e654";
}
.van-icon-contact:before {
    content: "\e753";
}
.van-icon-notes:before {
    content: "\e63c";
}
.van-icon-records:before {
    content: "\e63d";
}
.van-icon-cash-back-record:before {
    content: "\e63e";
}
.van-icon-newspaper:before {
    content: "\e63f";
}
.van-icon-discount:before {
    content: "\e640";
}
.van-icon-completed:before {
    content: "\e641";
}
.van-icon-user:before {
    content: "\e642";
}
.van-icon-description:before {
    content: "\e643";
}
.van-icon-list-switch:before {
    content: "\e6ad";
}
.van-icon-list-switching:before {
    content: "\e65a";
}
.van-icon-link-o:before {
    content: "\e751";
}
.van-icon-miniprogram-o:before {
    content: "\e752";
}
.van-icon-qq:before {
    content: "\e74e";
}
.van-icon-wechat-moments:before {
    content: "\e74f";
}
.van-icon-weibo:before {
    content: "\e750";
}
.van-icon-cash-o:before {
    content: "\e74d";
}
.van-icon-guide-o:before {
    content: "\e74c";
}
.van-icon-invitation:before {
    content: "\e6d6";
}
.van-icon-shield-o:before {
    content: "\e74b";
}
.van-icon-exchange:before {
    content: "\e6af";
}
.van-icon-eye:before {
    content: "\e6b0";
}
.van-icon-enlarge:before {
    content: "\e6b1";
}
.van-icon-expand-o:before {
    content: "\e6b2";
}
.van-icon-eye-o:before {
    content: "\e6b3";
}
.van-icon-expand:before {
    content: "\e6b4";
}
.van-icon-filter-o:before {
    content: "\e6b5";
}
.van-icon-fire:before {
    content: "\e6b6";
}
.van-icon-fail:before {
    content: "\e6b7";
}
.van-icon-failure:before {
    content: "\e6b8";
}
.van-icon-fire-o:before {
    content: "\e6b9";
}
.van-icon-flag-o:before {
    content: "\e6ba";
}
.van-icon-font:before {
    content: "\e6bb";
}
.van-icon-font-o:before {
    content: "\e6bc";
}
.van-icon-gem-o:before {
    content: "\e6bd";
}
.van-icon-flower-o:before {
    content: "\e6be";
}
.van-icon-gem:before {
    content: "\e6bf";
}
.van-icon-gift-card:before {
    content: "\e6c0";
}
.van-icon-friends:before {
    content: "\e6c1";
}
.van-icon-friends-o:before {
    content: "\e6c2";
}
.van-icon-gold-coin:before {
    content: "\e6c3";
}
.van-icon-gold-coin-o:before {
    content: "\e6c4";
}
.van-icon-good-job-o:before {
    content: "\e6c5";
}
.van-icon-gift:before {
    content: "\e6c6";
}
.van-icon-gift-o:before {
    content: "\e6c7";
}
.van-icon-gift-card-o:before {
    content: "\e6c8";
}
.van-icon-good-job:before {
    content: "\e6c9";
}
.van-icon-home-o:before {
    content: "\e6ca";
}
.van-icon-goods-collect:before {
    content: "\e6cb";
}
.van-icon-graphic:before {
    content: "\e6cc";
}
.van-icon-goods-collect-o:before {
    content: "\e6cd";
}
.van-icon-hot-o:before {
    content: "\e6ce";
}
.van-icon-info:before {
    content: "\e6cf";
}
.van-icon-hotel-o:before {
    content: "\e6d0";
}
.van-icon-info-o:before {
    content: "\e6d1";
}
.van-icon-hot-sale-o:before {
    content: "\e6d2";
}
.van-icon-hot:before {
    content: "\e6d3";
}
.van-icon-like:before {
    content: "\e6d4";
}
.van-icon-idcard:before {
    content: "\e6d5";
}
.van-icon-like-o:before {
    content: "\e6d7";
}
.van-icon-hot-sale:before {
    content: "\e6d8";
}
.van-icon-location-o:before {
    content: "\e6d9";
}
.van-icon-location:before {
    content: "\e6da";
}
.van-icon-label:before {
    content: "\e6db";
}
.van-icon-lock:before {
    content: "\e6dc";
}
.van-icon-label-o:before {
    content: "\e6dd";
}
.van-icon-map-marked:before {
    content: "\e6de";
}
.van-icon-logistics:before {
    content: "\e6df";
}
.van-icon-manager:before {
    content: "\e6e0";
}
.van-icon-more:before {
    content: "\e6e1";
}
.van-icon-live:before {
    content: "\e6e2";
}
.van-icon-manager-o:before {
    content: "\e6e3";
}
.van-icon-medal:before {
    content: "\e6e4";
}
.van-icon-more-o:before {
    content: "\e6e5";
}
.van-icon-music-o:before {
    content: "\e6e6";
}
.van-icon-music:before {
    content: "\e6e7";
}
.van-icon-new-arrival-o:before {
    content: "\e6e8";
}
.van-icon-medal-o:before {
    content: "\e6e9";
}
.van-icon-new-o:before {
    content: "\e6ea";
}
.van-icon-free-postage:before {
    content: "\e6eb";
}
.van-icon-newspaper-o:before {
    content: "\e6ec";
}
.van-icon-new-arrival:before {
    content: "\e6ed";
}
.van-icon-minus:before {
    content: "\e6ee";
}
.van-icon-orders-o:before {
    content: "\e6ef";
}
.van-icon-new:before {
    content: "\e6f0";
}
.van-icon-paid:before {
    content: "\e6f1";
}
.van-icon-notes-o:before {
    content: "\e6f2";
}
.van-icon-other-pay:before {
    content: "\e6f3";
}
.van-icon-pause-circle:before {
    content: "\e6f4";
}
.van-icon-pause:before {
    content: "\e6f5";
}
.van-icon-pause-circle-o:before {
    content: "\e6f6";
}
.van-icon-peer-pay:before {
    content: "\e6f7";
}
.van-icon-pending-payment:before {
    content: "\e6f8";
}
.van-icon-passed:before {
    content: "\e6f9";
}
.van-icon-plus:before {
    content: "\e6fa";
}
.van-icon-phone-circle-o:before {
    content: "\e6fb";
}
.van-icon-phone-o:before {
    content: "\e6fc";
}
.van-icon-printer:before {
    content: "\e6fd";
}
.van-icon-photo-fail:before {
    content: "\e6fe";
}
.van-icon-phone:before {
    content: "\e6ff";
}
.van-icon-photo-o:before {
    content: "\e700";
}
.van-icon-play-circle:before {
    content: "\e701";
}
.van-icon-play:before {
    content: "\e702";
}
.van-icon-phone-circle:before {
    content: "\e703";
}
.van-icon-point-gift-o:before {
    content: "\e704";
}
.van-icon-point-gift:before {
    content: "\e705";
}
.van-icon-play-circle-o:before {
    content: "\e706";
}
.van-icon-shrink:before {
    content: "\e707";
}
.van-icon-photo:before {
    content: "\e708";
}
.van-icon-qr:before {
    content: "\e709";
}
.van-icon-qr-invalid:before {
    content: "\e70a";
}
.van-icon-question-o:before {
    content: "\e70b";
}
.van-icon-revoke:before {
    content: "\e70c";
}
.van-icon-replay:before {
    content: "\e70d";
}
.van-icon-service:before {
    content: "\e70e";
}
.van-icon-question:before {
    content: "\e70f";
}
.van-icon-search:before {
    content: "\e710";
}
.van-icon-refund-o:before {
    content: "\e711";
}
.van-icon-service-o:before {
    content: "\e712";
}
.van-icon-scan:before {
    content: "\e713";
}
.van-icon-share:before {
    content: "\e714";
}
.van-icon-send-gift-o:before {
    content: "\e715";
}
.van-icon-share-o:before {
    content: "\e716";
}
.van-icon-setting:before {
    content: "\e717";
}
.van-icon-points:before {
    content: "\e718";
}
.van-icon-photograph:before {
    content: "\e719";
}
.van-icon-shop:before {
    content: "\e71a";
}
.van-icon-shop-o:before {
    content: "\e71b";
}
.van-icon-shop-collect-o:before {
    content: "\e71c";
}
.van-icon-shop-collect:before {
    content: "\e71d";
}
.van-icon-smile:before {
    content: "\e71e";
}
.van-icon-shopping-cart-o:before {
    content: "\e71f";
}
.van-icon-sign:before {
    content: "\e720";
}
.van-icon-sort:before {
    content: "\e721";
}
.van-icon-star-o:before {
    content: "\e722";
}
.van-icon-smile-comment-o:before {
    content: "\e723";
}
.van-icon-stop:before {
    content: "\e724";
}
.van-icon-stop-circle-o:before {
    content: "\e725";
}
.van-icon-smile-o:before {
    content: "\e726";
}
.van-icon-star:before {
    content: "\e727";
}
.van-icon-success:before {
    content: "\e728";
}
.van-icon-stop-circle:before {
    content: "\e729";
}
.van-icon-records-o:before {
    content: "\e72a";
}
.van-icon-shopping-cart:before {
    content: "\e72b";
}
.van-icon-tosend:before {
    content: "\e72c";
}
.van-icon-todo-list:before {
    content: "\e72d";
}
.van-icon-thumb-circle-o:before {
    content: "\e72e";
}
.van-icon-thumb-circle:before {
    content: "\e72f";
}
.van-icon-umbrella-circle:before {
    content: "\e730";
}
.van-icon-underway:before {
    content: "\e731";
}
.van-icon-upgrade:before {
    content: "\e732";
}
.van-icon-todo-list-o:before {
    content: "\e733";
}
.van-icon-tv-o:before {
    content: "\e734";
}
.van-icon-underway-o:before {
    content: "\e735";
}
.van-icon-user-o:before {
    content: "\e736";
}
.van-icon-vip-card-o:before {
    content: "\e737";
}
.van-icon-vip-card:before {
    content: "\e738";
}
.van-icon-send-gift:before {
    content: "\e739";
}
.van-icon-wap-home:before {
    content: "\e73a";
}
.van-icon-wap-nav:before {
    content: "\e73b";
}
.van-icon-volume-o:before {
    content: "\e73c";
}
.van-icon-video:before {
    content: "\e73d";
}
.van-icon-wap-home-o:before {
    content: "\e73e";
}
.van-icon-volume:before {
    content: "\e73f";
}
.van-icon-warning:before {
    content: "\e740";
}
.van-icon-weapp-nav:before {
    content: "\e741";
}
.van-icon-wechat-pay:before {
    content: "\e742";
}
.van-icon-warning-o:before {
    content: "\e743";
}
.van-icon-wechat:before {
    content: "\e744";
}
.van-icon-setting-o:before {
    content: "\e745";
}
.van-icon-youzan-shield:before {
    content: "\e746";
}
.van-icon-warn-o:before {
    content: "\e747";
}
.van-icon-smile-comment:before {
    content: "\e748";
}
.van-icon-user-circle-o:before {
    content: "\e749";
}
.van-icon-video-o:before {
    content: "\e74a";
}
.van-icon-add-square:before {
    content: "\e65c";
}
.van-icon-add:before {
    content: "\e65d";
}
.van-icon-arrow-down:before {
    content: "\e65e";
}
.van-icon-arrow-up:before {
    content: "\e65f";
}
.van-icon-arrow:before {
    content: "\e660";
}
.van-icon-after-sale:before {
    content: "\e661";
}
.van-icon-add-o:before {
    content: "\e662";
}
.van-icon-alipay:before {
    content: "\e663";
}
.van-icon-ascending:before {
    content: "\e664";
}
.van-icon-apps-o:before {
    content: "\e665";
}
.van-icon-aim:before {
    content: "\e666";
}
.van-icon-award:before {
    content: "\e667";
}
.van-icon-arrow-left:before {
    content: "\e668";
}
.van-icon-award-o:before {
    content: "\e669";
}
.van-icon-audio:before {
    content: "\e66a";
}
.van-icon-bag-o:before {
    content: "\e66b";
}
.van-icon-balance-list:before {
    content: "\e66c";
}
.van-icon-back-top:before {
    content: "\e66d";
}
.van-icon-bag:before {
    content: "\e66e";
}
.van-icon-balance-pay:before {
    content: "\e66f";
}
.van-icon-balance-o:before {
    content: "\e670";
}
.van-icon-bar-chart-o:before {
    content: "\e671";
}
.van-icon-bars:before {
    content: "\e672";
}
.van-icon-balance-list-o:before {
    content: "\e673";
}
.van-icon-birthday-cake-o:before {
    content: "\e674";
}
.van-icon-bookmark:before {
    content: "\e675";
}
.van-icon-bill:before {
    content: "\e676";
}
.van-icon-bell:before {
    content: "\e677";
}
.van-icon-browsing-history-o:before {
    content: "\e678";
}
.van-icon-browsing-history:before {
    content: "\e679";
}
.van-icon-bookmark-o:before {
    content: "\e67a";
}
.van-icon-bulb-o:before {
    content: "\e67b";
}
.van-icon-bullhorn-o:before {
    content: "\e67c";
}
.van-icon-bill-o:before {
    content: "\e67d";
}
.van-icon-calendar-o:before {
    content: "\e67e";
}
.van-icon-brush-o:before {
    content: "\e67f";
}
.van-icon-card:before {
    content: "\e680";
}
.van-icon-cart-o:before {
    content: "\e681";
}
.van-icon-cart-circle:before {
    content: "\e682";
}
.van-icon-cart-circle-o:before {
    content: "\e683";
}
.van-icon-cart:before {
    content: "\e684";
}
.van-icon-cash-on-deliver:before {
    content: "\e685";
}
.van-icon-cash-back-record-o:before {
    content: "\e686";
}
.van-icon-cashier-o:before {
    content: "\e687";
}
.van-icon-chart-trending-o:before {
    content: "\e688";
}
.van-icon-certificate:before {
    content: "\e689";
}
.van-icon-chat:before {
    content: "\e68a";
}
.van-icon-clear:before {
    content: "\e68b";
}
.van-icon-chat-o:before {
    content: "\e68c";
}
.van-icon-checked:before {
    content: "\e68d";
}
.van-icon-clock:before {
    content: "\e68e";
}
.van-icon-clock-o:before {
    content: "\e68f";
}
.van-icon-close:before {
    content: "\e690";
}
.van-icon-closed-eye:before {
    content: "\e691";
}
.van-icon-circle:before {
    content: "\e692";
}
.van-icon-cluster-o:before {
    content: "\e693";
}
.van-icon-column:before {
    content: "\e694";
}
.van-icon-comment-circle-o:before {
    content: "\e695";
}
.van-icon-cluster:before {
    content: "\e696";
}
.van-icon-comment:before {
    content: "\e697";
}
.van-icon-comment-o:before {
    content: "\e698";
}
.van-icon-comment-circle:before {
    content: "\e699";
}
.van-icon-completed-o:before {
    content: "\e69a";
}
.van-icon-credit-pay:before {
    content: "\e69b";
}
.van-icon-coupon:before {
    content: "\e69c";
}
.van-icon-debit-pay:before {
    content: "\e69d";
}
.van-icon-coupon-o:before {
    content: "\e69e";
}
.van-icon-contact-o:before {
    content: "\e69f";
}
.van-icon-descending:before {
    content: "\e6a0";
}
.van-icon-desktop-o:before {
    content: "\e6a1";
}
.van-icon-diamond-o:before {
    content: "\e6a2";
}
.van-icon-description-o:before {
    content: "\e6a3";
}
.van-icon-delete:before {
    content: "\e6a4";
}
.van-icon-diamond:before {
    content: "\e6a5";
}
.van-icon-delete-o:before {
    content: "\e6a6";
}
.van-icon-cross:before {
    content: "\e6a7";
}
.van-icon-edit:before {
    content: "\e6a8";
}
.van-icon-ellipsis:before {
    content: "\e6a9";
}
.van-icon-down:before {
    content: "\e6aa";
}
.van-icon-discount-o:before {
    content: "\e6ab";
}
.van-icon-ecard-pay:before {
    content: "\e6ac";
}
.van-icon-envelop-o:before {
    content: "\e6ae";
}
@font-face {
    font-weight: 400;
    font-family: vant-icon;
    font-style: normal;
    font-display: auto;
    src: url(data:font/woff2;charset=utf-8;base64,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)
            format("woff2"),
        url(//at.alicdn.com/t/c/font_2553510_ciljc7axaw7.woff?t=1705587463221) format("woff");
}
.van-icon__image {
    display: block;
    width: 1em;
    height: 1em;
    -o-object-fit: contain;
    object-fit: contain;
}
:host,
:root {
    --van-skeleton-image-size: 96px;
    --van-skeleton-image-radius: 24px;
}
.van-skeleton-image {
    display: flex;
    width: var(--van-skeleton-image-size);
    height: var(--van-skeleton-image-size);
    align-items: center;
    justify-content: center;
    background: var(--van-active-color);
}
.van-skeleton-image--round {
    border-radius: var(--van-skeleton-image-radius);
}
.van-skeleton-image__icon {
    width: calc(var(--van-skeleton-image-size) / 2);
    height: calc(var(--van-skeleton-image-size) / 2);
    font-size: calc(var(--van-skeleton-image-size) / 2);
    color: var(--van-gray-5);
}
:host,
:root {
    --van-rate-icon-size: 20px;
    --van-rate-icon-gutter: var(--van-padding-base);
    --van-rate-icon-void-color: var(--van-gray-5);
    --van-rate-icon-full-color: var(--van-danger-color);
    --van-rate-icon-disabled-color: var(--van-gray-5);
}
.van-rate {
    display: inline-flex;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    flex-wrap: wrap;
}
.van-rate__item {
    position: relative;
}
.van-rate__item:not(:last-child) {
    padding-right: var(--van-rate-icon-gutter);
}
.van-rate__icon {
    display: block;
    width: 1em;
    color: var(--van-rate-icon-void-color);
    font-size: var(--van-rate-icon-size);
}
.van-rate__icon--half {
    position: absolute;
    top: 0;
    left: 0;
    overflow: hidden;
    pointer-events: none;
}
.van-rate__icon--full {
    color: var(--van-rate-icon-full-color);
}
.van-rate__icon--disabled {
    color: var(--van-rate-icon-disabled-color);
}
.van-rate--disabled {
    cursor: not-allowed;
}
.van-rate--readonly {
    cursor: default;
}
:host,
:root {
    --van-notice-bar-height: 40px;
    --van-notice-bar-padding: 0 var(--van-padding-md);
    --van-notice-bar-wrapable-padding: var(--van-padding-xs) var(--van-padding-md);
    --van-notice-bar-text-color: var(--van-orange-dark);
    --van-notice-bar-font-size: var(--van-font-size-md);
    --van-notice-bar-line-height: 24px;
    --van-notice-bar-background: var(--van-orange-light);
    --van-notice-bar-icon-size: 16px;
    --van-notice-bar-icon-min-width: 24px;
}
.van-notice-bar {
    position: relative;
    display: flex;
    align-items: center;
    height: var(--van-notice-bar-height);
    padding: var(--van-notice-bar-padding);
    color: var(--van-notice-bar-text-color);
    font-size: var(--van-notice-bar-font-size);
    line-height: var(--van-notice-bar-line-height);
    background: var(--van-notice-bar-background);
}
.van-notice-bar__left-icon,
.van-notice-bar__right-icon {
    min-width: var(--van-notice-bar-icon-min-width);
    font-size: var(--van-notice-bar-icon-size);
}
.van-notice-bar__right-icon {
    text-align: right;
    cursor: pointer;
}
.van-notice-bar__wrap {
    position: relative;
    display: flex;
    flex: 1;
    align-items: center;
    height: 100%;
    overflow: hidden;
}
.van-notice-bar__content {
    position: absolute;
    white-space: nowrap;
    transition-timing-function: linear;
}
.van-notice-bar__content.van-ellipsis {
    max-width: 100%;
}
.van-notice-bar--wrapable {
    height: auto;
    padding: var(--van-notice-bar-wrapable-padding);
}
.van-notice-bar--wrapable .van-notice-bar__wrap {
    height: auto;
}
.van-notice-bar--wrapable .van-notice-bar__content {
    position: relative;
    white-space: normal;
    word-wrap: break-word;
}
:host,
:root {
    --van-nav-bar-height: 46px;
    --van-nav-bar-background: var(--van-background-2);
    --van-nav-bar-arrow-size: 16px;
    --van-nav-bar-icon-color: var(--van-primary-color);
    --van-nav-bar-text-color: var(--van-primary-color);
    --van-nav-bar-title-font-size: var(--van-font-size-lg);
    --van-nav-bar-title-text-color: var(--van-text-color);
    --van-nav-bar-z-index: 1;
    --van-nav-bar-disabled-opacity: var(--van-disabled-opacity);
}
.van-nav-bar {
    position: relative;
    z-index: var(--van-nav-bar-z-index);
    line-height: var(--van-line-height-lg);
    text-align: center;
    background: var(--van-nav-bar-background);
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
}
.van-nav-bar--fixed {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
}
.van-nav-bar--safe-area-inset-top {
    padding-top: constant(safe-area-inset-top);
    padding-top: env(safe-area-inset-top);
}
.van-nav-bar .van-icon {
    color: var(--van-nav-bar-icon-color);
}
.van-nav-bar__content {
    position: relative;
    display: flex;
    align-items: center;
    height: var(--van-nav-bar-height);
}
.van-nav-bar__arrow {
    margin-right: var(--van-padding-base);
    font-size: var(--van-nav-bar-arrow-size);
}
.van-nav-bar__title {
    max-width: 60%;
    margin: 0 auto;
    color: var(--van-nav-bar-title-text-color);
    font-weight: var(--van-font-bold);
    font-size: var(--van-nav-bar-title-font-size);
}
.van-nav-bar__left,
.van-nav-bar__right {
    position: absolute;
    top: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    padding: 0 var(--van-padding-md);
    font-size: var(--van-font-size-md);
}
.van-nav-bar__left--disabled,
.van-nav-bar__right--disabled {
    cursor: not-allowed;
    opacity: var(--van-nav-bar-disabled-opacity);
}
.van-nav-bar__left {
    left: 0;
}
.van-nav-bar__right {
    right: 0;
}
.van-nav-bar__text {
    color: var(--van-nav-bar-text-color);
}
:host,
:root {
    --van-floating-bubble-size: 48px;
    --van-floating-bubble-initial-gap: 24px;
    --van-floating-bubble-icon-size: 28px;
    --van-floating-bubble-background: var(--van-primary-color);
    --van-floating-bubble-color: var(--van-background-2);
    --van-floating-bubble-z-index: 999;
    --van-floating-bubble-border-radius: var(--van-radius-max);
}
.van-floating-bubble {
    position: fixed;
    left: 0;
    top: 0;
    right: var(--van-floating-bubble-initial-gap);
    bottom: var(--van-floating-bubble-initial-gap);
    width: var(--van-floating-bubble-size);
    height: var(--van-floating-bubble-size);
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    touch-action: none;
    background: var(--van-floating-bubble-background);
    color: var(--van-floating-bubble-color);
    border-radius: var(--van-floating-bubble-border-radius);
    z-index: var(--van-floating-bubble-z-index);
    transition: transform var(--van-duration-base);
}
.van-floating-bubble:active {
    opacity: 0.8;
}
.van-floating-bubble__icon {
    font-size: var(--van-floating-bubble-icon-size);
}
:host,
:root {
    --van-image-placeholder-text-color: var(--van-text-color-2);
    --van-image-placeholder-font-size: var(--van-font-size-md);
    --van-image-placeholder-background: var(--van-background);
    --van-image-loading-icon-size: 32px;
    --van-image-loading-icon-color: var(--van-gray-4);
    --van-image-error-icon-size: 32px;
    --van-image-error-icon-color: var(--van-gray-4);
}
.van-image {
    position: relative;
    display: inline-block;
}
.van-image--round {
    overflow: hidden;
    border-radius: var(--van-radius-max);
}
.van-image--round .van-image__img {
    border-radius: inherit;
}
.van-image--block {
    display: block;
}
.van-image__error,
.van-image__img,
.van-image__loading {
    display: block;
    width: 100%;
    height: 100%;
}
.van-image__error,
.van-image__loading {
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--van-image-placeholder-text-color);
    font-size: var(--van-image-placeholder-font-size);
    background: var(--van-image-placeholder-background);
}
.van-image__loading-icon {
    color: var(--van-image-loading-icon-color);
    font-size: var(--van-image-loading-icon-size);
}
.van-image__error-icon {
    color: var(--van-image-error-icon-color);
    font-size: var(--van-image-error-icon-size);
}
:host,
:root {
    --van-back-top-size: 40px;
    --van-back-top-right: 30px;
    --van-back-top-bottom: 40px;
    --van-back-top-z-index: 100;
    --van-back-top-icon-size: 20px;
    --van-back-top-text-color: #fff;
    --van-back-top-background: var(--van-blue);
}
.van-back-top {
    position: fixed;
    display: flex;
    align-items: center;
    justify-content: center;
    width: var(--van-back-top-size);
    height: var(--van-back-top-size);
    right: var(--van-back-top-right);
    bottom: var(--van-back-top-bottom);
    z-index: var(--van-back-top-z-index);
    cursor: pointer;
    color: var(--van-back-top-text-color);
    border-radius: var(--van-radius-max);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
    transform: scale(0);
    transition: var(--van-duration-base) cubic-bezier(0.25, 0.8, 0.5, 1);
    background-color: var(--van-back-top-background);
}
.van-back-top:active {
    opacity: var(--van-active-opacity);
}
.van-back-top__placeholder {
    display: none;
}
.van-back-top--active {
    transform: scale(1);
}
.van-back-top__icon {
    font-size: var(--van-back-top-icon-size);
    font-weight: var(--van-font-bold);
}
:host,
:root {
    --van-tag-padding: 0 var(--van-padding-base);
    --van-tag-text-color: var(--van-white);
    --van-tag-font-size: var(--van-font-size-sm);
    --van-tag-radius: 2px;
    --van-tag-line-height: 16px;
    --van-tag-medium-padding: 2px 6px;
    --van-tag-large-padding: var(--van-padding-base) var(--van-padding-xs);
    --van-tag-large-radius: var(--van-radius-md);
    --van-tag-large-font-size: var(--van-font-size-md);
    --van-tag-round-radius: var(--van-radius-max);
    --van-tag-danger-color: var(--van-danger-color);
    --van-tag-primary-color: var(--van-primary-color);
    --van-tag-success-color: var(--van-success-color);
    --van-tag-warning-color: var(--van-warning-color);
    --van-tag-default-color: var(--van-gray-6);
    --van-tag-plain-background: var(--van-background-2);
}
.van-tag {
    position: relative;
    display: inline-flex;
    align-items: center;
    padding: var(--van-tag-padding);
    color: var(--van-tag-text-color);
    font-size: var(--van-tag-font-size);
    line-height: var(--van-tag-line-height);
    border-radius: var(--van-tag-radius);
}
.van-tag--default {
    background: var(--van-tag-default-color);
}
.van-tag--default.van-tag--plain {
    color: var(--van-tag-default-color);
}
.van-tag--danger {
    background: var(--van-tag-danger-color);
}
.van-tag--danger.van-tag--plain {
    color: var(--van-tag-danger-color);
}
.van-tag--primary {
    background: var(--van-tag-primary-color);
}
.van-tag--primary.van-tag--plain {
    color: var(--van-tag-primary-color);
}
.van-tag--success {
    background: var(--van-tag-success-color);
}
.van-tag--success.van-tag--plain {
    color: var(--van-tag-success-color);
}
.van-tag--warning {
    background: var(--van-tag-warning-color);
}
.van-tag--warning.van-tag--plain {
    color: var(--van-tag-warning-color);
}
.van-tag--plain {
    background: var(--van-tag-plain-background);
    border-color: currentColor;
}
.van-tag--plain:before {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    border: 1px solid;
    border-color: inherit;
    border-radius: inherit;
    content: "";
    pointer-events: none;
}
.van-tag--medium {
    padding: var(--van-tag-medium-padding);
}
.van-tag--large {
    padding: var(--van-tag-large-padding);
    font-size: var(--van-tag-large-font-size);
    border-radius: var(--van-tag-large-radius);
}
.van-tag--mark {
    border-radius: 0 var(--van-tag-round-radius) var(--van-tag-round-radius) 0;
}
.van-tag--mark:after {
    display: block;
    width: 2px;
    content: "";
}
.van-tag--round {
    border-radius: var(--van-tag-round-radius);
}
.van-tag__close {
    margin-left: 2px;
}
:host,
:root {
    --van-card-padding: var(--van-padding-xs) var(--van-padding-md);
    --van-card-font-size: var(--van-font-size-sm);
    --van-card-text-color: var(--van-text-color);
    --van-card-background: var(--van-background);
    --van-card-thumb-size: 88px;
    --van-card-thumb-radius: var(--van-radius-lg);
    --van-card-title-line-height: 16px;
    --van-card-desc-color: var(--van-text-color-2);
    --van-card-desc-line-height: var(--van-line-height-md);
    --van-card-price-color: var(--van-text-color);
    --van-card-origin-price-color: var(--van-text-color-2);
    --van-card-num-color: var(--van-text-color-2);
    --van-card-origin-price-font-size: var(--van-font-size-xs);
    --van-card-price-font-size: var(--van-font-size-sm);
    --van-card-price-integer-font-size: var(--van-font-size-lg);
    --van-card-price-font: var(--van-price-font);
}
.van-card {
    position: relative;
    box-sizing: border-box;
    padding: var(--van-card-padding);
    color: var(--van-card-text-color);
    font-size: var(--van-card-font-size);
    background: var(--van-card-background);
}
.van-card:not(:first-child) {
    margin-top: var(--van-padding-xs);
}
.van-card__header {
    display: flex;
}
.van-card__thumb {
    position: relative;
    flex: none;
    width: var(--van-card-thumb-size);
    height: var(--van-card-thumb-size);
    margin-right: var(--van-padding-xs);
}
.van-card__thumb img {
    border-radius: var(--van-card-thumb-radius);
}
.van-card__content {
    position: relative;
    display: flex;
    flex: 1;
    flex-direction: column;
    justify-content: space-between;
    min-width: 0;
    min-height: var(--van-card-thumb-size);
}
.van-card__content--centered {
    justify-content: center;
}
.van-card__desc,
.van-card__title {
    word-wrap: break-word;
}
.van-card__title {
    max-height: 32px;
    font-weight: var(--van-font-bold);
    line-height: var(--van-card-title-line-height);
}
.van-card__desc {
    max-height: var(--van-card-desc-line-height);
    color: var(--van-card-desc-color);
    line-height: var(--van-card-desc-line-height);
}
.van-card__bottom {
    line-height: var(--van-line-height-md);
}
.van-card__price {
    display: inline-block;
    color: var(--van-card-price-color);
    font-weight: var(--van-font-bold);
    font-size: var(--van-card-price-font-size);
}
.van-card__price-integer {
    font-size: var(--van-card-price-integer-font-size);
}
.van-card__price-decimal,
.van-card__price-integer {
    font-family: var(--van-card-price-font);
}
.van-card__origin-price {
    display: inline-block;
    margin-left: 5px;
    color: var(--van-card-origin-price-color);
    font-size: var(--van-card-origin-price-font-size);
    text-decoration: line-through;
}
.van-card__num {
    float: right;
    color: var(--van-card-num-color);
}
.van-card__tag {
    position: absolute;
    top: 2px;
    left: 0;
}
.van-card__footer {
    flex: none;
    text-align: right;
}
.van-card__footer .van-button {
    margin-left: 5px;
}
:host,
:root {
    --van-cell-font-size: var(--van-font-size-md);
    --van-cell-line-height: 24px;
    --van-cell-vertical-padding: 10px;
    --van-cell-horizontal-padding: var(--van-padding-md);
    --van-cell-text-color: var(--van-text-color);
    --van-cell-background: var(--van-background-2);
    --van-cell-border-color: var(--van-border-color);
    --van-cell-active-color: var(--van-active-color);
    --van-cell-required-color: var(--van-danger-color);
    --van-cell-label-color: var(--van-text-color-2);
    --van-cell-label-font-size: var(--van-font-size-sm);
    --van-cell-label-line-height: var(--van-line-height-sm);
    --van-cell-label-margin-top: var(--van-padding-base);
    --van-cell-value-color: var(--van-text-color-2);
    --van-cell-value-font-size: inherit;
    --van-cell-icon-size: 16px;
    --van-cell-right-icon-color: var(--van-gray-6);
    --van-cell-large-vertical-padding: var(--van-padding-sm);
    --van-cell-large-title-font-size: var(--van-font-size-lg);
    --van-cell-large-label-font-size: var(--van-font-size-md);
    --van-cell-large-value-font-size: inherit;
}
.van-cell {
    position: relative;
    display: flex;
    box-sizing: border-box;
    width: 100%;
    padding: var(--van-cell-vertical-padding) var(--van-cell-horizontal-padding);
    overflow: hidden;
    color: var(--van-cell-text-color);
    font-size: var(--van-cell-font-size);
    line-height: var(--van-cell-line-height);
    background: var(--van-cell-background);
}
.van-cell:after {
    position: absolute;
    box-sizing: border-box;
    content: " ";
    pointer-events: none;
    right: var(--van-padding-md);
    bottom: 0;
    left: var(--van-padding-md);
    border-bottom: 1px solid var(--van-cell-border-color);
    transform: scaleY(0.5);
}
.van-cell--borderless:after,
.van-cell:last-child:after {
    display: none;
}
.van-cell__label {
    margin-top: var(--van-cell-label-margin-top);
    color: var(--van-cell-label-color);
    font-size: var(--van-cell-label-font-size);
    line-height: var(--van-cell-label-line-height);
}
.van-cell__title,
.van-cell__value {
    flex: 1;
}
.van-cell__value {
    position: relative;
    overflow: hidden;
    color: var(--van-cell-value-color);
    font-size: var(--van-cell-value-font-size);
    text-align: right;
    vertical-align: middle;
    word-wrap: break-word;
}
.van-cell__left-icon,
.van-cell__right-icon {
    height: var(--van-cell-line-height);
    font-size: var(--van-cell-icon-size);
    line-height: var(--van-cell-line-height);
}
.van-cell__left-icon {
    margin-right: var(--van-padding-base);
}
.van-cell__right-icon {
    margin-left: var(--van-padding-base);
    color: var(--van-cell-right-icon-color);
}
.van-cell--clickable {
    cursor: pointer;
}
.van-cell--clickable:active {
    background-color: var(--van-cell-active-color);
}
.van-cell--required {
    overflow: visible;
}
.van-cell--required:before {
    position: absolute;
    left: var(--van-padding-xs);
    color: var(--van-cell-required-color);
    font-size: var(--van-cell-font-size);
    content: "*";
}
.van-cell--center {
    align-items: center;
}
.van-cell--large {
    padding-top: var(--van-cell-large-vertical-padding);
    padding-bottom: var(--van-cell-large-vertical-padding);
}
.van-cell--large .van-cell__title {
    font-size: var(--van-cell-large-title-font-size);
}
.van-cell--large .van-cell__label {
    font-size: var(--van-cell-large-label-font-size);
}
.van-cell--large .van-cell__value {
    font-size: var(--van-cell-large-value-font-size);
}
:host,
:root {
    --van-coupon-cell-selected-text-color: var(--van-text-color);
}
.van-coupon-cell__value--selected {
    color: var(--van-coupon-cell-selected-text-color);
}
:host,
:root {
    --van-contact-card-padding: var(--van-padding-md);
    --van-contact-card-add-icon-size: 40px;
    --van-contact-card-add-icon-color: var(--van-primary-color);
    --van-contact-card-title-line-height: var(--van-line-height-md);
}
.van-contact-card {
    padding: var(--van-contact-card-padding);
}
.van-contact-card__title {
    margin-left: 5px;
    line-height: var(--van-contact-card-title-line-height);
}
.van-contact-card--add .van-contact-card__value {
    line-height: var(--van-contact-card-add-icon-size);
}
.van-contact-card--add .van-cell__left-icon {
    color: var(--van-contact-card-add-icon-color);
    font-size: var(--van-contact-card-add-icon-size);
}
.van-contact-card:before {
    position: absolute;
    right: 0;
    bottom: 0;
    left: 0;
    height: 2px;
    background: repeating-linear-gradient(-45deg, var(--van-warning-color) 0, var(--van-warning-color) 20%, transparent 0, transparent 25%, var(--van-primary-color) 0, var(--van-primary-color) 45%, transparent 0, transparent 50%);
    background-size: 80px;
    content: "";
}
:host,
:root {
    --van-collapse-item-duration: var(--van-duration-base);
    --van-collapse-item-content-padding: var(--van-padding-sm) var(--van-padding-md);
    --van-collapse-item-content-font-size: var(--van-font-size-md);
    --van-collapse-item-content-line-height: 1.5;
    --van-collapse-item-content-text-color: var(--van-text-color-2);
    --van-collapse-item-content-background: var(--van-background-2);
    --van-collapse-item-title-disabled-color: var(--van-text-color-3);
}
.van-collapse-item {
    position: relative;
}
.van-collapse-item--border:after {
    position: absolute;
    box-sizing: border-box;
    content: " ";
    pointer-events: none;
    top: 0;
    right: var(--van-padding-md);
    left: var(--van-padding-md);
    border-top: 1px solid var(--van-border-color);
    transform: scaleY(0.5);
}
.van-collapse-item__title .van-cell__right-icon:before {
    transform: rotate(90deg) translateZ(0);
    transition: transform var(--van-collapse-item-duration);
}
.van-collapse-item__title:after {
    right: var(--van-padding-md);
    display: none;
}
.van-collapse-item__title--expanded .van-cell__right-icon:before {
    transform: rotate(-90deg);
}
.van-collapse-item__title--expanded:after {
    display: block;
}
.van-collapse-item__title--borderless:after {
    display: none;
}
.van-collapse-item__title--disabled {
    cursor: not-allowed;
}
.van-collapse-item__title--disabled,
.van-collapse-item__title--disabled .van-cell__right-icon {
    color: var(--van-collapse-item-title-disabled-color);
}
.van-collapse-item__wrapper {
    overflow: hidden;
    transition: height var(--van-collapse-item-duration) ease-in-out;
    will-change: height;
}
.van-collapse-item__content {
    padding: var(--van-collapse-item-content-padding);
    color: var(--van-collapse-item-content-text-color);
    font-size: var(--van-collapse-item-content-font-size);
    line-height: var(--van-collapse-item-content-line-height);
    background: var(--van-collapse-item-content-background);
}
:host,
:root {
    --van-field-label-width: 6.2em;
    --van-field-label-color: var(--van-text-color);
    --van-field-label-margin-right: var(--van-padding-sm);
    --van-field-input-text-color: var(--van-text-color);
    --van-field-input-error-text-color: var(--van-danger-color);
    --van-field-input-disabled-text-color: var(--van-text-color-3);
    --van-field-placeholder-text-color: var(--van-text-color-3);
    --van-field-icon-size: 18px;
    --van-field-clear-icon-size: 18px;
    --van-field-clear-icon-color: var(--van-gray-5);
    --van-field-right-icon-color: var(--van-gray-6);
    --van-field-error-message-color: var(--van-danger-color);
    --van-field-error-message-font-size: 12px;
    --van-field-text-area-min-height: 60px;
    --van-field-word-limit-color: var(--van-gray-7);
    --van-field-word-limit-font-size: var(--van-font-size-sm);
    --van-field-word-limit-line-height: 16px;
    --van-field-disabled-text-color: var(--van-text-color-3);
    --van-field-required-mark-color: var(--van-red);
}
.van-field {
    flex-wrap: wrap;
}
.van-field__label {
    flex: none;
    box-sizing: border-box;
    width: var(--van-field-label-width);
    margin-right: var(--van-field-label-margin-right);
    color: var(--van-field-label-color);
    text-align: left;
    word-wrap: break-word;
}
.van-field__label--center {
    text-align: center;
}
.van-field__label--right {
    text-align: right;
}
.van-field__label--top {
    display: flex;
    width: 100%;
    text-align: left;
    margin-bottom: var(--van-padding-base);
    overflow-wrap: break-word;
}
.van-field__label--required:before {
    margin-right: 2px;
    color: var(--van-field-required-mark-color);
    content: "*";
}
.van-field--disabled .van-field__label {
    color: var(--van-field-disabled-text-color);
}
.van-field__value {
    overflow: visible;
}
.van-field__body {
    display: flex;
    align-items: center;
}
.van-field__control {
    display: block;
    box-sizing: border-box;
    width: 100%;
    min-width: 0;
    margin: 0;
    padding: 0;
    color: var(--van-field-input-text-color);
    line-height: inherit;
    text-align: left;
    background-color: transparent;
    border: 0;
    resize: none;
    -webkit-user-select: auto;
    -moz-user-select: auto;
    user-select: auto;
}
.van-field__control::-moz-placeholder {
    color: var(--van-field-placeholder-text-color);
}
.van-field__control::placeholder {
    color: var(--van-field-placeholder-text-color);
}
.van-field__control:-moz-read-only {
    cursor: default;
}
.van-field__control:read-only {
    cursor: default;
}
.van-field__control:disabled {
    color: var(--van-field-input-disabled-text-color);
    cursor: not-allowed;
    opacity: 1;
    -webkit-text-fill-color: var(--van-field-input-disabled-text-color);
}
.van-field__control--center {
    justify-content: center;
    text-align: center;
}
.van-field__control--right {
    justify-content: flex-end;
    text-align: right;
}
.van-field__control--custom {
    display: flex;
    align-items: center;
    min-height: var(--van-cell-line-height);
}
.van-field__control--error::-moz-placeholder {
    color: var(--van-field-input-error-text-color);
    -webkit-text-fill-color: currentColor;
}
.van-field__control--error,
.van-field__control--error::placeholder {
    color: var(--van-field-input-error-text-color);
    -webkit-text-fill-color: currentColor;
}
.van-field__control--min-height {
    min-height: var(--van-field-text-area-min-height);
}
.van-field__control[type="date"],
.van-field__control[type="datetime-local"],
.van-field__control[type="time"] {
    min-height: var(--van-cell-line-height);
}
.van-field__control[type="search"] {
    -webkit-appearance: none;
}
.van-field__button,
.van-field__clear,
.van-field__icon,
.van-field__right-icon {
    flex-shrink: 0;
}
.van-field__clear,
.van-field__right-icon {
    margin-right: calc(var(--van-padding-xs) * -1);
    padding: 0 var(--van-padding-xs);
    line-height: inherit;
}
.van-field__clear {
    color: var(--van-field-clear-icon-color);
    font-size: var(--van-field-clear-icon-size);
    cursor: pointer;
}
.van-field__left-icon .van-icon,
.van-field__right-icon .van-icon {
    display: block;
    font-size: var(--van-field-icon-size);
    line-height: inherit;
}
.van-field__left-icon {
    margin-right: var(--van-padding-base);
}
.van-field__right-icon {
    color: var(--van-field-right-icon-color);
}
.van-field__button {
    padding-left: var(--van-padding-xs);
}
.van-field__error-message {
    color: var(--van-field-error-message-color);
    font-size: var(--van-field-error-message-font-size);
    text-align: left;
}
.van-field__error-message--center {
    text-align: center;
}
.van-field__error-message--right {
    text-align: right;
}
.van-field__word-limit {
    margin-top: var(--van-padding-base);
    color: var(--van-field-word-limit-color);
    font-size: var(--van-field-word-limit-font-size);
    line-height: var(--van-field-word-limit-line-height);
    text-align: right;
}
:host,
:root {
    --van-search-padding: 10px var(--van-padding-sm);
    --van-search-background: var(--van-background-2);
    --van-search-content-background: var(--van-background);
    --van-search-input-height: 34px;
    --van-search-label-padding: 0 5px;
    --van-search-label-color: var(--van-text-color);
    --van-search-label-font-size: var(--van-font-size-md);
    --van-search-left-icon-color: var(--van-gray-6);
    --van-search-action-padding: 0 var(--van-padding-xs);
    --van-search-action-text-color: var(--van-text-color);
    --van-search-action-font-size: var(--van-font-size-md);
}
.van-search {
    display: flex;
    align-items: center;
    box-sizing: border-box;
    padding: var(--van-search-padding);
    background: var(--van-search-background);
}
.van-search__content {
    display: flex;
    flex: 1;
    padding-left: var(--van-padding-sm);
    background: var(--van-search-content-background);
    border-radius: var(--van-radius-sm);
}
.van-search__content--round {
    border-radius: var(--van-radius-max);
}
.van-search__label {
    padding: var(--van-search-label-padding);
    color: var(--van-search-label-color);
    font-size: var(--van-search-label-font-size);
    line-height: var(--van-search-input-height);
}
.van-search__field {
    flex: 1;
    align-items: center;
    padding: 0 var(--van-padding-xs) 0 0;
    height: var(--van-search-input-height);
    background-color: transparent;
}
.van-search__field .van-field__left-icon {
    color: var(--van-search-left-icon-color);
}
.van-search__field--with-message {
    height: auto;
    align-items: flex-start;
    padding-top: 5px;
    padding-bottom: 5px;
}
.van-search--show-action {
    padding-right: 0;
}
.van-search input::-webkit-search-cancel-button,
.van-search input::-webkit-search-decoration,
.van-search input::-webkit-search-results-button,
.van-search input::-webkit-search-results-decoration {
    display: none;
}
.van-search__action {
    padding: var(--van-search-action-padding);
    color: var(--van-search-action-text-color);
    font-size: var(--van-search-action-font-size);
    line-height: var(--van-search-input-height);
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
}
.van-search__action:active {
    background-color: var(--van-active-color);
}
:host,
:root {
    --van-action-bar-icon-width: 48px;
    --van-action-bar-icon-height: 100%;
    --van-action-bar-icon-color: var(--van-text-color);
    --van-action-bar-icon-size: 18px;
    --van-action-bar-icon-font-size: var(--van-font-size-xs);
    --van-action-bar-icon-active-color: var(--van-active-color);
    --van-action-bar-icon-text-color: var(--van-text-color);
    --van-action-bar-icon-background: var(--van-background-2);
}
.van-action-bar-icon {
    display: flex;
    flex-direction: column;
    justify-content: center;
    min-width: var(--van-action-bar-icon-width);
    height: var(--van-action-bar-icon-height);
    color: var(--van-action-bar-icon-text-color);
    font-size: var(--van-action-bar-icon-font-size);
    line-height: 1;
    text-align: center;
    background: var(--van-action-bar-icon-background);
    cursor: pointer;
}
.van-action-bar-icon:active {
    background-color: var(--van-action-bar-icon-active-color);
}
.van-action-bar-icon__icon {
    margin: 0 auto var(--van-padding-base);
    color: var(--van-action-bar-icon-color);
    font-size: var(--van-action-bar-icon-size);
}
:host,
:root {
    --van-loading-text-color: var(--van-text-color-2);
    --van-loading-text-font-size: var(--van-font-size-md);
    --van-loading-spinner-color: var(--van-gray-5);
    --van-loading-spinner-size: 30px;
    --van-loading-spinner-duration: 0.8s;
}
.van-loading {
    color: var(--van-loading-spinner-color);
    font-size: 0;
}
.van-loading,
.van-loading__spinner {
    position: relative;
    vertical-align: middle;
}
.van-loading__spinner {
    display: inline-block;
    width: var(--van-loading-spinner-size);
    max-width: 100%;
    height: var(--van-loading-spinner-size);
    max-height: 100%;
    animation: van-rotate var(--van-loading-spinner-duration) linear infinite;
}
.van-loading__spinner--spinner {
    animation-timing-function: steps(12);
}
.van-loading__spinner--circular {
    animation-duration: 2s;
}
.van-loading__line {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}
.van-loading__line:before {
    display: block;
    width: 2px;
    height: 25%;
    margin: 0 auto;
    background-color: currentColor;
    border-radius: 40%;
    content: " ";
}
.van-loading__circular {
    display: block;
    width: 100%;
    height: 100%;
}
.van-loading__circular circle {
    animation: van-circular 1.5s ease-in-out infinite;
    stroke: currentColor;
    stroke-width: 3;
    stroke-linecap: round;
}
.van-loading__text {
    display: inline-block;
    margin-left: var(--van-padding-xs);
    color: var(--van-loading-text-color);
    font-size: var(--van-loading-text-font-size);
    vertical-align: middle;
}
.van-loading--vertical {
    display: flex;
    flex-direction: column;
    align-items: center;
}
.van-loading--vertical .van-loading__text {
    margin: var(--van-padding-xs) 0 0;
}
@keyframes van-circular {
    0% {
        stroke-dasharray: 1, 200;
        stroke-dashoffset: 0;
    }
    50% {
        stroke-dasharray: 90, 150;
        stroke-dashoffset: -40;
    }
    to {
        stroke-dasharray: 90, 150;
        stroke-dashoffset: -120;
    }
}
.van-loading__line--1 {
    transform: rotate(30deg);
    opacity: 1;
}
.van-loading__line--2 {
    transform: rotate(60deg);
    opacity: 0.9375;
}
.van-loading__line--3 {
    transform: rotate(90deg);
    opacity: 0.875;
}
.van-loading__line--4 {
    transform: rotate(120deg);
    opacity: 0.8125;
}
.van-loading__line--5 {
    transform: rotate(150deg);
    opacity: 0.75;
}
.van-loading__line--6 {
    transform: rotate(180deg);
    opacity: 0.6875;
}
.van-loading__line--7 {
    transform: rotate(210deg);
    opacity: 0.625;
}
.van-loading__line--8 {
    transform: rotate(240deg);
    opacity: 0.5625;
}
.van-loading__line--9 {
    transform: rotate(270deg);
    opacity: 0.5;
}
.van-loading__line--10 {
    transform: rotate(300deg);
    opacity: 0.4375;
}
.van-loading__line--11 {
    transform: rotate(330deg);
    opacity: 0.375;
}
.van-loading__line--12 {
    transform: rotate(1turn);
    opacity: 0.3125;
}
:host,
:root {
    --van-pull-refresh-head-height: 50px;
    --van-pull-refresh-head-font-size: var(--van-font-size-md);
    --van-pull-refresh-head-text-color: var(--van-text-color-2);
    --van-pull-refresh-loading-icon-size: 16px;
}
.van-pull-refresh {
    overflow: hidden;
}
.van-pull-refresh__track {
    position: relative;
    height: 100%;
    transition-property: transform;
}
.van-pull-refresh__head {
    position: absolute;
    left: 0;
    width: 100%;
    height: var(--van-pull-refresh-head-height);
    overflow: hidden;
    color: var(--van-pull-refresh-head-text-color);
    font-size: var(--van-pull-refresh-head-font-size);
    line-height: var(--van-pull-refresh-head-height);
    text-align: center;
    transform: translateY(-100%);
}
.van-pull-refresh__loading .van-loading__spinner {
    width: var(--van-pull-refresh-loading-icon-size);
    height: var(--van-pull-refresh-loading-icon-size);
}
:host,
:root {
    --van-number-keyboard-background: var(--van-gray-2);
    --van-number-keyboard-key-height: 48px;
    --van-number-keyboard-key-font-size: 28px;
    --van-number-keyboard-key-active-color: var(--van-gray-3);
    --van-number-keyboard-key-background: var(--van-background-2);
    --van-number-keyboard-delete-font-size: var(--van-font-size-lg);
    --van-number-keyboard-title-color: var(--van-gray-7);
    --van-number-keyboard-title-height: 34px;
    --van-number-keyboard-title-font-size: var(--van-font-size-lg);
    --van-number-keyboard-close-padding: 0 var(--van-padding-md);
    --van-number-keyboard-close-color: var(--van-primary-color);
    --van-number-keyboard-close-font-size: var(--van-font-size-md);
    --van-number-keyboard-button-text-color: var(--van-white);
    --van-number-keyboard-button-background: var(--van-primary-color);
    --van-number-keyboard-z-index: 100;
}
.van-theme-dark {
    --van-number-keyboard-background: var(--van-gray-8);
    --van-number-keyboard-key-background: var(--van-gray-7);
    --van-number-keyboard-key-active-color: var(--van-gray-6);
}
.van-number-keyboard {
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: var(--van-number-keyboard-z-index);
    width: 100%;
    padding-bottom: 22px;
    background: var(--van-number-keyboard-background);
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
}
.van-number-keyboard--with-title {
    border-radius: 20px 20px 0 0;
}
.van-number-keyboard__header {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: content-box;
    height: var(--van-number-keyboard-title-height);
    padding-top: 6px;
    color: var(--van-number-keyboard-title-color);
    font-size: var(--van-number-keyboard-title-font-size);
}
.van-number-keyboard__title {
    display: inline-block;
    font-weight: 400;
}
.van-number-keyboard__title-left {
    position: absolute;
    left: 0;
}
.van-number-keyboard__body {
    display: flex;
    padding: 6px 0 0 6px;
}
.van-number-keyboard__keys {
    display: flex;
    flex: 3;
    flex-wrap: wrap;
}
.van-number-keyboard__close {
    position: absolute;
    right: 0;
    height: 100%;
    padding: var(--van-number-keyboard-close-padding);
    color: var(--van-number-keyboard-close-color);
    font-size: var(--van-number-keyboard-close-font-size);
    background-color: transparent;
    border: none;
}
.van-number-keyboard__sidebar {
    display: flex;
    flex: 1;
    flex-direction: column;
}
.van-number-keyboard--unfit {
    padding-bottom: 0;
}
.van-key {
    display: flex;
    align-items: center;
    justify-content: center;
    height: var(--van-number-keyboard-key-height);
    font-size: var(--van-number-keyboard-key-font-size);
    line-height: 1.5;
    background: var(--van-number-keyboard-key-background);
    border-radius: var(--van-radius-lg);
    cursor: pointer;
}
.van-key--large {
    position: absolute;
    top: 0;
    right: 6px;
    bottom: 6px;
    left: 0;
    height: auto;
}
.van-key--blue,
.van-key--delete {
    font-size: var(--van-number-keyboard-delete-font-size);
}
.van-key--active {
    background-color: var(--van-number-keyboard-key-active-color);
}
.van-key--blue {
    color: var(--van-number-keyboard-button-text-color);
    background: var(--van-number-keyboard-button-background);
}
.van-key--blue.van-key--active {
    opacity: var(--van-active-opacity);
}
.van-key__wrapper {
    position: relative;
    flex: 1;
    flex-basis: 33%;
    box-sizing: border-box;
    padding: 0 6px 6px 0;
}
.van-key__wrapper--wider {
    flex-basis: 66%;
}
.van-key__delete-icon {
    width: 32px;
    height: 22px;
}
.van-key__collapse-icon {
    width: 30px;
    height: 24px;
}
.van-key__loading-icon {
    color: var(--van-number-keyboard-button-text-color);
}
:host,
:root {
    --van-list-text-color: var(--van-text-color-2);
    --van-list-text-font-size: var(--van-font-size-md);
    --van-list-text-line-height: 50px;
    --van-list-loading-icon-size: 16px;
}
.van-list__error-text,
.van-list__finished-text,
.van-list__loading {
    color: var(--van-list-text-color);
    font-size: var(--van-list-text-font-size);
    line-height: var(--van-list-text-line-height);
    text-align: center;
}
.van-list__placeholder {
    height: 0;
    pointer-events: none;
}
.van-list__loading-icon .van-loading__spinner {
    width: var(--van-list-loading-icon-size);
    height: var(--van-list-loading-icon-size);
}
:host,
:root {
    --van-switch-size: 26px;
    --van-switch-width: calc(1.8em + 4px);
    --van-switch-height: calc(1em + 4px);
    --van-switch-node-size: 1em;
    --van-switch-node-background: var(--van-white);
    --van-switch-node-shadow: 0 3px 1px 0 rgba(0, 0, 0, 0.05);
    --van-switch-background: hsla(240, 3%, 49%, 0.16);
    --van-switch-on-background: var(--van-primary-color);
    --van-switch-duration: var(--van-duration-base);
    --van-switch-disabled-opacity: var(--van-disabled-opacity);
}
.van-theme-dark {
    --van-switch-background: hsla(240, 3%, 49%, 0.32);
}
.van-switch {
    position: relative;
    display: inline-block;
    box-sizing: content-box;
    width: var(--van-switch-width);
    height: var(--van-switch-height);
    font-size: var(--van-switch-size);
    background: var(--van-switch-background);
    border-radius: var(--van-switch-node-size);
    cursor: pointer;
    transition: background-color var(--van-switch-duration);
}
.van-switch__node {
    position: absolute;
    top: 2px;
    left: 2px;
    width: var(--van-switch-node-size);
    height: var(--van-switch-node-size);
    font-size: inherit;
    background: var(--van-switch-node-background);
    border-radius: 100%;
    box-shadow: var(--van-switch-node-shadow);
    transition: transform var(--van-switch-duration) cubic-bezier(0.3, 1.05, 0.4, 1.05);
}
.van-switch__loading {
    top: 25%;
    left: 25%;
    width: 50%;
    height: 50%;
    line-height: 1;
}
.van-switch--on {
    background: var(--van-switch-on-background);
}
.van-switch--on .van-switch__node {
    transform: translate(calc(var(--van-switch-width) - var(--van-switch-node-size) - 4px));
}
.van-switch--on .van-switch__loading {
    color: var(--van-switch-on-background);
}
.van-switch--disabled {
    cursor: not-allowed;
    opacity: var(--van-switch-disabled-opacity);
}
.van-switch--loading {
    cursor: default;
}
:host,
:root {
    --van-button-mini-height: 24px;
    --van-button-mini-padding: 0 var(--van-padding-base);
    --van-button-mini-font-size: var(--van-font-size-xs);
    --van-button-small-height: 32px;
    --van-button-small-padding: 0 var(--van-padding-xs);
    --van-button-small-font-size: var(--van-font-size-sm);
    --van-button-normal-padding: 0 15px;
    --van-button-normal-font-size: var(--van-font-size-md);
    --van-button-large-height: 50px;
    --van-button-default-height: 44px;
    --van-button-default-line-height: 1.2;
    --van-button-default-font-size: var(--van-font-size-lg);
    --van-button-default-color: var(--van-text-color);
    --van-button-default-background: var(--van-background-2);
    --van-button-default-border-color: var(--van-gray-4);
    --van-button-primary-color: var(--van-white);
    --van-button-primary-background: var(--van-primary-color);
    --van-button-primary-border-color: var(--van-primary-color);
    --van-button-success-color: var(--van-white);
    --van-button-success-background: var(--van-success-color);
    --van-button-success-border-color: var(--van-success-color);
    --van-button-danger-color: var(--van-white);
    --van-button-danger-background: var(--van-danger-color);
    --van-button-danger-border-color: var(--van-danger-color);
    --van-button-warning-color: var(--van-white);
    --van-button-warning-background: var(--van-warning-color);
    --van-button-warning-border-color: var(--van-warning-color);
    --van-button-border-width: var(--van-border-width);
    --van-button-radius: var(--van-radius-md);
    --van-button-round-radius: var(--van-radius-max);
    --van-button-plain-background: var(--van-white);
    --van-button-disabled-opacity: var(--van-disabled-opacity);
    --van-button-icon-size: 1.2em;
    --van-button-loading-icon-size: 20px;
}
.van-theme-dark {
    --van-button-plain-background: transparent;
}
.van-button {
    position: relative;
    display: inline-block;
    box-sizing: border-box;
    height: var(--van-button-default-height);
    margin: 0;
    padding: 0;
    font-size: var(--van-button-default-font-size);
    line-height: var(--van-button-default-line-height);
    text-align: center;
    border-radius: var(--van-button-radius);
    cursor: pointer;
    transition: opacity var(--van-duration-fast);
    -webkit-appearance: none;
    -webkit-font-smoothing: auto;
}
.van-button:before {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    height: 100%;
    background: var(--van-black);
    border: inherit;
    border-color: var(--van-black);
    border-radius: inherit;
    transform: translate(-50%, -50%);
    opacity: 0;
    content: " ";
}
.van-button:active:before {
    opacity: 0.1;
}
.van-button--disabled:before,
.van-button--loading:before {
    display: none;
}
.van-button--default {
    color: var(--van-button-default-color);
    background: var(--van-button-default-background);
    border: var(--van-button-border-width) solid var(--van-button-default-border-color);
}
.van-button--primary {
    color: var(--van-button-primary-color);
    background: var(--van-button-primary-background);
    border: var(--van-button-border-width) solid var(--van-button-primary-border-color);
}
.van-button--success {
    color: var(--van-button-success-color);
    background: var(--van-button-success-background);
    border: var(--van-button-border-width) solid var(--van-button-success-border-color);
}
.van-button--danger {
    color: var(--van-button-danger-color);
    background: var(--van-button-danger-background);
    border: var(--van-button-border-width) solid var(--van-button-danger-border-color);
}
.van-button--warning {
    color: var(--van-button-warning-color);
    background: var(--van-button-warning-background);
    border: var(--van-button-border-width) solid var(--van-button-warning-border-color);
}
.van-button--plain {
    background: var(--van-button-plain-background);
}
.van-button--plain.van-button--primary {
    color: var(--van-button-primary-background);
}
.van-button--plain.van-button--success {
    color: var(--van-button-success-background);
}
.van-button--plain.van-button--danger {
    color: var(--van-button-danger-background);
}
.van-button--plain.van-button--warning {
    color: var(--van-button-warning-background);
}
.van-button--large {
    width: 100%;
    height: var(--van-button-large-height);
}
.van-button--normal {
    padding: var(--van-button-normal-padding);
    font-size: var(--van-button-normal-font-size);
}
.van-button--small {
    height: var(--van-button-small-height);
    padding: var(--van-button-small-padding);
    font-size: var(--van-button-small-font-size);
}
.van-button__loading {
    color: inherit;
    font-size: inherit;
}
.van-button__loading .van-loading__spinner {
    color: currentColor;
    width: var(--van-button-loading-icon-size);
    height: var(--van-button-loading-icon-size);
}
.van-button--mini {
    height: var(--van-button-mini-height);
    padding: var(--van-button-mini-padding);
    font-size: var(--van-button-mini-font-size);
}
.van-button--mini + .van-button--mini {
    margin-left: var(--van-padding-base);
}
.van-button--block {
    display: block;
    width: 100%;
}
.van-button--disabled {
    cursor: not-allowed;
    opacity: var(--van-button-disabled-opacity);
}
.van-button--loading {
    cursor: default;
}
.van-button--round {
    border-radius: var(--van-button-round-radius);
}
.van-button--square {
    border-radius: 0;
}
.van-button__content {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
}
.van-button__content:before {
    content: " ";
}
.van-button__icon {
    font-size: var(--van-button-icon-size);
    line-height: inherit;
}
.van-button__icon + .van-button__text,
.van-button__loading + .van-button__text,
.van-button__text + .van-button__icon,
.van-button__text + .van-button__loading {
    margin-left: var(--van-padding-base);
}
.van-button--hairline {
    border-width: 0;
}
.van-button--hairline:after {
    border-color: inherit;
    border-radius: calc(var(--van-button-radius) * 2);
}
.van-button--hairline.van-button--round:after {
    border-radius: var(--van-button-round-radius);
}
.van-button--hairline.van-button--square:after {
    border-radius: 0;
}
:host,
:root {
    --van-submit-bar-height: 50px;
    --van-submit-bar-z-index: 100;
    --van-submit-bar-background: var(--van-background-2);
    --van-submit-bar-button-width: 110px;
    --van-submit-bar-price-color: var(--van-danger-color);
    --van-submit-bar-price-font-size: var(--van-font-size-sm);
    --van-submit-bar-price-integer-font-size: 20px;
    --van-submit-bar-price-font: var(--van-price-font);
    --van-submit-bar-text-color: var(--van-text-color);
    --van-submit-bar-text-font-size: var(--van-font-size-md);
    --van-submit-bar-tip-padding: var(--van-padding-xs) var(--van-padding-sm);
    --van-submit-bar-tip-font-size: var(--van-font-size-sm);
    --van-submit-bar-tip-line-height: 1.5;
    --van-submit-bar-tip-color: var(--van-orange-dark);
    --van-submit-bar-tip-background: var(--van-orange-light);
    --van-submit-bar-tip-icon-size: 12px;
    --van-submit-bar-button-height: 40px;
    --van-submit-bar-padding: 0 var(--van-padding-md);
}
.van-submit-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: var(--van-submit-bar-z-index);
    width: 100%;
    background: var(--van-submit-bar-background);
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
}
.van-submit-bar__tip {
    padding: var(--van-submit-bar-tip-padding);
    color: var(--van-submit-bar-tip-color);
    font-size: var(--van-submit-bar-tip-font-size);
    line-height: var(--van-submit-bar-tip-line-height);
    background: var(--van-submit-bar-tip-background);
}
.van-submit-bar__tip-icon {
    margin-right: var(--van-padding-base);
    font-size: var(--van-submit-bar-tip-icon-size);
    vertical-align: middle;
}
.van-submit-bar__tip-text {
    vertical-align: middle;
}
.van-submit-bar__bar {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    height: var(--van-submit-bar-height);
    padding: var(--van-submit-bar-padding);
    font-size: var(--van-submit-bar-text-font-size);
}
.van-submit-bar__text {
    flex: 1;
    padding-right: var(--van-padding-sm);
    color: var(--van-submit-bar-text-color);
    text-align: right;
}
.van-submit-bar__text span {
    display: inline-block;
}
.van-submit-bar__price,
.van-submit-bar__suffix-label {
    margin-left: var(--van-padding-base);
    font-weight: var(--van-font-bold);
}
.van-submit-bar__price {
    color: var(--van-submit-bar-price-color);
    font-size: var(--van-submit-bar-price-font-size);
}
.van-submit-bar__price-integer {
    font-size: var(--van-submit-bar-price-integer-font-size);
    font-family: var(--van-submit-bar-price-font);
}
.van-submit-bar__button {
    width: var(--van-submit-bar-button-width);
    height: var(--van-submit-bar-button-height);
    font-weight: var(--van-font-bold);
    border: none;
}
.van-submit-bar__button--danger {
    background: var(--van-gradient-red);
}
:host,
:root {
    --van-signature-padding: var(--van-padding-xs);
    --van-signature-content-height: 200px;
    --van-signature-content-background: var(--van-background-2);
    --van-signature-content-border: 1px dotted #dadada;
}
.van-signature {
    padding: var(--van-signature-padding);
}
.van-signature__content {
    display: flex;
    justify-content: center;
    align-items: center;
    height: var(--van-signature-content-height);
    background-color: var(--van-signature-content-background);
    border: var(--van-signature-content-border);
    border-radius: var(--van-radius-lg);
    overflow: hidden;
}
.van-signature__content canvas {
    width: 100%;
    height: 100%;
}
.van-signature__footer {
    display: flex;
    justify-content: flex-end;
}
.van-signature__footer .van-button {
    padding: 0 var(--van-padding-md);
    margin-top: var(--van-padding-xs);
    margin-left: var(--van-padding-xs);
}
:host,
:root {
    --van-contact-edit-padding: var(--van-padding-md);
    --van-contact-edit-fields-radius: var(--van-radius-md);
    --van-contact-edit-buttons-padding: var(--van-padding-xl) 0;
    --van-contact-edit-button-margin-bottom: var(--van-padding-sm);
    --van-contact-edit-button-font-size: var(--van-font-size-lg);
    --van-contact-edit-field-label-width: 4.1em;
}
.van-contact-edit {
    padding: var(--van-contact-edit-padding);
}
.van-contact-edit__fields {
    overflow: hidden;
    border-radius: var(--van-contact-edit-fields-radius);
}
.van-contact-edit__fields .van-field__label {
    width: var(--van-contact-edit-field-label-width);
}
.van-contact-edit__switch-cell {
    margin-top: 10px;
    padding-top: 9px;
    padding-bottom: 9px;
    border-radius: var(--van-contact-edit-fields-radius);
}
.van-contact-edit__buttons {
    padding: var(--van-contact-edit-buttons-padding);
}
.van-contact-edit__button {
    margin-bottom: var(--van-contact-edit-button-margin-bottom);
    font-size: var(--van-contact-edit-button-font-size);
}
:host,
:root {
    --van-action-bar-button-height: 40px;
    --van-action-bar-button-warning-color: var(--van-gradient-orange);
    --van-action-bar-button-danger-color: var(--van-gradient-red);
}
.van-action-bar-button {
    flex: 1;
    height: var(--van-action-bar-button-height);
    font-weight: var(--van-font-bold);
    font-size: var(--van-font-size-md);
    border: none;
    border-radius: 0;
}
.van-action-bar-button--first {
    margin-left: 5px;
    border-top-left-radius: var(--van-radius-max);
    border-bottom-left-radius: var(--van-radius-max);
}
.van-action-bar-button--last {
    margin-right: 5px;
    border-top-right-radius: var(--van-radius-max);
    border-bottom-right-radius: var(--van-radius-max);
}
.van-action-bar-button--warning {
    background: var(--van-action-bar-button-warning-color);
}
.van-action-bar-button--danger {
    background: var(--van-action-bar-button-danger-color);
}
@media (max-width: 321px) {
    .van-action-bar-button {
        font-size: 13px;
    }
}
:host,
:root {
    --van-overlay-z-index: 1;
    --van-overlay-background: rgba(0, 0, 0, 0.7);
}
.van-overlay {
    position: fixed;
    top: 0;
    left: 0;
    z-index: var(--van-overlay-z-index);
    width: 100%;
    height: 100%;
    background: var(--van-overlay-background);
}
:host,
:root {
    --van-popup-background: var(--van-background-2);
    --van-popup-transition: transform var(--van-duration-base);
    --van-popup-round-radius: 16px;
    --van-popup-close-icon-size: 22px;
    --van-popup-close-icon-color: var(--van-gray-5);
    --van-popup-close-icon-margin: 16px;
    --van-popup-close-icon-z-index: 1;
}
.van-overflow-hidden {
    overflow: hidden !important;
}
.van-popup {
    position: fixed;
    max-height: 100%;
    overflow-y: auto;
    box-sizing: border-box;
    background: var(--van-popup-background);
    transition: var(--van-popup-transition);
    -webkit-overflow-scrolling: touch;
}
.van-popup--center {
    top: 50%;
    left: 0;
    right: 0;
    width: -moz-fit-content;
    width: fit-content;
    max-width: calc(100vw - var(--van-padding-md) * 2);
    margin: 0 auto;
    transform: translateY(-50%);
}
.van-popup--center.van-popup--round {
    border-radius: var(--van-popup-round-radius);
}
.van-popup--top {
    top: 0;
    left: 0;
    width: 100%;
}
.van-popup--top.van-popup--round {
    border-radius: 0 0 var(--van-popup-round-radius) var(--van-popup-round-radius);
}
.van-popup--right {
    top: 50%;
    right: 0;
    transform: translate3d(0, -50%, 0);
}
.van-popup--right.van-popup--round {
    border-radius: var(--van-popup-round-radius) 0 0 var(--van-popup-round-radius);
}
.van-popup--bottom {
    bottom: 0;
    left: 0;
    width: 100%;
}
.van-popup--bottom.van-popup--round {
    border-radius: var(--van-popup-round-radius) var(--van-popup-round-radius) 0 0;
}
.van-popup--left {
    top: 50%;
    left: 0;
    transform: translate3d(0, -50%, 0);
}
.van-popup--left.van-popup--round {
    border-radius: 0 var(--van-popup-round-radius) var(--van-popup-round-radius) 0;
}
.van-popup-slide-bottom-enter-active,
.van-popup-slide-left-enter-active,
.van-popup-slide-right-enter-active,
.van-popup-slide-top-enter-active {
    transition-timing-function: var(--van-ease-out);
}
.van-popup-slide-bottom-leave-active,
.van-popup-slide-left-leave-active,
.van-popup-slide-right-leave-active,
.van-popup-slide-top-leave-active {
    transition-timing-function: var(--van-ease-in);
}
.van-popup-slide-top-enter-from,
.van-popup-slide-top-leave-active {
    transform: translate3d(0, -100%, 0);
}
.van-popup-slide-right-enter-from,
.van-popup-slide-right-leave-active {
    transform: translate3d(100%, -50%, 0);
}
.van-popup-slide-bottom-enter-from,
.van-popup-slide-bottom-leave-active {
    transform: translate3d(0, 100%, 0);
}
.van-popup-slide-left-enter-from,
.van-popup-slide-left-leave-active {
    transform: translate3d(-100%, -50%, 0);
}
.van-popup__close-icon {
    position: absolute;
    z-index: var(--van-popup-close-icon-z-index);
    color: var(--van-popup-close-icon-color);
    font-size: var(--van-popup-close-icon-size);
}
.van-popup__close-icon--top-left {
    top: var(--van-popup-close-icon-margin);
    left: var(--van-popup-close-icon-margin);
}
.van-popup__close-icon--top-right {
    top: var(--van-popup-close-icon-margin);
    right: var(--van-popup-close-icon-margin);
}
.van-popup__close-icon--bottom-left {
    bottom: var(--van-popup-close-icon-margin);
    left: var(--van-popup-close-icon-margin);
}
.van-popup__close-icon--bottom-right {
    right: var(--van-popup-close-icon-margin);
    bottom: var(--van-popup-close-icon-margin);
}
:host,
:root {
    --van-share-sheet-header-padding: var(--van-padding-sm) var(--van-padding-md);
    --van-share-sheet-title-color: var(--van-text-color);
    --van-share-sheet-title-font-size: var(--van-font-size-md);
    --van-share-sheet-title-line-height: var(--van-line-height-md);
    --van-share-sheet-description-color: var(--van-text-color-2);
    --van-share-sheet-description-font-size: var(--van-font-size-sm);
    --van-share-sheet-description-line-height: 16px;
    --van-share-sheet-icon-size: 48px;
    --van-share-sheet-option-name-color: var(--van-gray-7);
    --van-share-sheet-option-name-font-size: var(--van-font-size-sm);
    --van-share-sheet-option-description-color: var(--van-text-color-3);
    --van-share-sheet-option-description-font-size: var(--van-font-size-sm);
    --van-share-sheet-cancel-button-font-size: var(--van-font-size-lg);
    --van-share-sheet-cancel-button-height: 48px;
    --van-share-sheet-cancel-button-background: var(--van-background-2);
}
.van-share-sheet__header {
    padding: var(--van-share-sheet-header-padding);
    text-align: center;
}
.van-share-sheet__title {
    margin-top: var(--van-padding-xs);
    color: var(--van-share-sheet-title-color);
    font-weight: 400;
    font-size: var(--van-share-sheet-title-font-size);
    line-height: var(--van-share-sheet-title-line-height);
}
.van-share-sheet__description {
    display: block;
    margin-top: var(--van-padding-xs);
    color: var(--van-share-sheet-description-color);
    font-size: var(--van-share-sheet-description-font-size);
    line-height: var(--van-share-sheet-description-line-height);
}
.van-share-sheet__options {
    position: relative;
    display: flex;
    padding: var(--van-padding-md) 0 var(--van-padding-md) var(--van-padding-xs);
    overflow-x: auto;
    overflow-y: visible;
    -webkit-overflow-scrolling: touch;
}
.van-share-sheet__options--border:before {
    position: absolute;
    box-sizing: border-box;
    content: " ";
    pointer-events: none;
    top: 0;
    right: 0;
    left: var(--van-padding-md);
    border-top: 1px solid var(--van-border-color);
    transform: scaleY(0.5);
}
.van-share-sheet__options::-webkit-scrollbar {
    height: 0;
}
.van-share-sheet__option {
    display: flex;
    flex-direction: column;
    align-items: center;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
}
.van-share-sheet__icon,
.van-share-sheet__image-icon {
    width: var(--van-share-sheet-icon-size);
    height: var(--van-share-sheet-icon-size);
    margin: 0 var(--van-padding-md);
}
.van-share-sheet__icon {
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--van-gray-7);
    border-radius: 100%;
    background-color: var(--van-gray-2);
}
.van-share-sheet__icon--link,
.van-share-sheet__icon--poster,
.van-share-sheet__icon--qrcode {
    font-size: 26px;
}
.van-share-sheet__icon--weapp-qrcode {
    font-size: 28px;
}
.van-share-sheet__icon--qq,
.van-share-sheet__icon--wechat,
.van-share-sheet__icon--wechat-moments,
.van-share-sheet__icon--weibo {
    font-size: 30px;
    color: var(--van-white);
}
.van-share-sheet__icon--qq {
    background-color: #38b9fa;
}
.van-share-sheet__icon--wechat {
    background-color: #0bc15f;
}
.van-share-sheet__icon--weibo {
    background-color: #ee575e;
}
.van-share-sheet__icon--wechat-moments {
    background-color: #7bc845;
}
.van-share-sheet__name {
    margin-top: var(--van-padding-xs);
    padding: 0 var(--van-padding-base);
    color: var(--van-share-sheet-option-name-color);
    font-size: var(--van-share-sheet-option-name-font-size);
}
.van-share-sheet__option-description {
    padding: 0 var(--van-padding-base);
    color: var(--van-share-sheet-option-description-color);
    font-size: var(--van-share-sheet-option-description-font-size);
}
.van-share-sheet__cancel {
    display: block;
    width: 100%;
    padding: 0;
    font-size: var(--van-share-sheet-cancel-button-font-size);
    line-height: var(--van-share-sheet-cancel-button-height);
    text-align: center;
    background: var(--van-share-sheet-cancel-button-background);
    border: none;
    cursor: pointer;
}
.van-share-sheet__cancel:before {
    display: block;
    height: var(--van-padding-xs);
    background-color: var(--van-background);
    content: " ";
}
.van-share-sheet__cancel:active {
    background-color: var(--van-active-color);
}
:host,
:root {
    --van-popover-arrow-size: 6px;
    --van-popover-radius: var(--van-radius-lg);
    --van-popover-action-width: 128px;
    --van-popover-action-height: 44px;
    --van-popover-action-font-size: var(--van-font-size-md);
    --van-popover-action-line-height: var(--van-line-height-md);
    --van-popover-action-icon-size: 20px;
    --van-popover-horizontal-action-height: 34px;
    --van-popover-horizontal-action-icon-size: 16px;
    --van-popover-light-text-color: var(--van-text-color);
    --van-popover-light-background: var(--van-background-2);
    --van-popover-light-action-disabled-text-color: var(--van-text-color-3);
    --van-popover-dark-text-color: var(--van-white);
    --van-popover-dark-background: #4a4a4a;
    --van-popover-dark-action-disabled-text-color: var(--van-text-color-2);
}
.van-popover {
    position: absolute;
    overflow: visible;
    background-color: transparent;
    transition: opacity 0.15s, transform 0.15s;
}
.van-popover__wrapper {
    display: inline-block;
}
.van-popover__arrow {
    position: absolute;
    width: 0;
    height: 0;
    border-color: transparent;
    border-style: solid;
    border-width: var(--van-popover-arrow-size);
}
.van-popover__content {
    overflow: hidden;
    border-radius: var(--van-popover-radius);
}
.van-popover__content--horizontal {
    display: flex;
    width: -moz-max-content;
    width: max-content;
}
.van-popover__content--horizontal .van-popover__action {
    flex: none;
    width: auto;
    height: var(--van-popover-horizontal-action-height);
    padding: 0 var(--van-padding-sm);
}
.van-popover__content--horizontal .van-popover__action:last-child:after {
    display: none;
}
.van-popover__content--horizontal .van-popover__action-icon {
    margin-right: var(--van-padding-base);
    font-size: var(--van-popover-horizontal-action-icon-size);
}
.van-popover__action {
    position: relative;
    display: flex;
    align-items: center;
    box-sizing: border-box;
    width: var(--van-popover-action-width);
    height: var(--van-popover-action-height);
    padding: 0 var(--van-padding-md);
    font-size: var(--van-popover-action-font-size);
    line-height: var(--van-line-height-md);
    cursor: pointer;
}
.van-popover__action:last-child .van-popover__action-text:after {
    display: none;
}
.van-popover__action-text {
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: center;
    height: 100%;
}
.van-popover__action-icon {
    margin-right: var(--van-padding-xs);
    font-size: var(--van-popover-action-icon-size);
}
.van-popover__action--with-icon .van-popover__action-text {
    justify-content: flex-start;
}
.van-popover[data-popper-placement^="top"] .van-popover__arrow {
    bottom: 0;
    border-top-color: currentColor;
    border-bottom-width: 0;
    margin-bottom: calc(var(--van-popover-arrow-size) * -1);
}
.van-popover[data-popper-placement="top"] {
    transform-origin: 50% 100%;
}
.van-popover[data-popper-placement="top"] .van-popover__arrow {
    left: 50%;
    transform: translate(-50%);
}
.van-popover[data-popper-placement="top-start"] {
    transform-origin: 0 100%;
}
.van-popover[data-popper-placement="top-start"] .van-popover__arrow {
    left: var(--van-padding-md);
}
.van-popover[data-popper-placement="top-end"] {
    transform-origin: 100% 100%;
}
.van-popover[data-popper-placement="top-end"] .van-popover__arrow {
    right: var(--van-padding-md);
}
.van-popover[data-popper-placement^="left"] .van-popover__arrow {
    right: 0;
    border-right-width: 0;
    border-left-color: currentColor;
    margin-right: calc(var(--van-popover-arrow-size) * -1);
}
.van-popover[data-popper-placement="left"] {
    transform-origin: 100% 50%;
}
.van-popover[data-popper-placement="left"] .van-popover__arrow {
    top: 50%;
    transform: translateY(-50%);
}
.van-popover[data-popper-placement="left-start"] {
    transform-origin: 100% 0;
}
.van-popover[data-popper-placement="left-start"] .van-popover__arrow {
    top: var(--van-padding-md);
}
.van-popover[data-popper-placement="left-end"] {
    transform-origin: 100% 100%;
}
.van-popover[data-popper-placement="left-end"] .van-popover__arrow {
    bottom: var(--van-padding-md);
}
.van-popover[data-popper-placement^="right"] .van-popover__arrow {
    left: 0;
    border-right-color: currentColor;
    border-left-width: 0;
    margin-left: calc(var(--van-popover-arrow-size) * -1);
}
.van-popover[data-popper-placement="right"] {
    transform-origin: 0 50%;
}
.van-popover[data-popper-placement="right"] .van-popover__arrow {
    top: 50%;
    transform: translateY(-50%);
}
.van-popover[data-popper-placement="right-start"] {
    transform-origin: 0 0;
}
.van-popover[data-popper-placement="right-start"] .van-popover__arrow {
    top: var(--van-padding-md);
}
.van-popover[data-popper-placement="right-end"] {
    transform-origin: 0 100%;
}
.van-popover[data-popper-placement="right-end"] .van-popover__arrow {
    bottom: var(--van-padding-md);
}
.van-popover[data-popper-placement^="bottom"] .van-popover__arrow {
    top: 0;
    border-top-width: 0;
    border-bottom-color: currentColor;
    margin-top: calc(var(--van-popover-arrow-size) * -1);
}
.van-popover[data-popper-placement="bottom"] {
    transform-origin: 50% 0;
}
.van-popover[data-popper-placement="bottom"] .van-popover__arrow {
    left: 50%;
    transform: translate(-50%);
}
.van-popover[data-popper-placement="bottom-start"] {
    transform-origin: 0 0;
}
.van-popover[data-popper-placement="bottom-start"] .van-popover__arrow {
    left: var(--van-padding-md);
}
.van-popover[data-popper-placement="bottom-end"] {
    transform-origin: 100% 0;
}
.van-popover[data-popper-placement="bottom-end"] .van-popover__arrow {
    right: var(--van-padding-md);
}
.van-popover--light {
    color: var(--van-popover-light-text-color);
}
.van-popover--light .van-popover__content {
    background: var(--van-popover-light-background);
    box-shadow: 0 2px 12px rgba(50, 50, 51, 0.12);
}
.van-popover--light .van-popover__arrow {
    color: var(--van-popover-light-background);
}
.van-popover--light .van-popover__action:active {
    background-color: var(--van-active-color);
}
.van-popover--light .van-popover__action--disabled {
    color: var(--van-popover-light-action-disabled-text-color);
    cursor: not-allowed;
}
.van-popover--light .van-popover__action--disabled:active {
    background-color: transparent;
}
.van-popover--dark {
    color: var(--van-popover-dark-text-color);
}
.van-popover--dark .van-popover__content {
    background: var(--van-popover-dark-background);
}
.van-popover--dark .van-popover__arrow {
    color: var(--van-popover-dark-background);
}
.van-popover--dark .van-popover__action:active {
    background-color: rgba(0, 0, 0, 0.2);
}
.van-popover--dark .van-popover__action--disabled {
    color: var(--van-popover-dark-action-disabled-text-color);
}
.van-popover--dark .van-popover__action--disabled:active {
    background-color: transparent;
}
.van-popover--dark .van-popover__action-text:after {
    border-color: var(--van-gray-7);
}
.van-popover-zoom-enter-from,
.van-popover-zoom-leave-active {
    transform: scale(0.8);
    opacity: 0;
}
.van-popover-zoom-enter-active {
    transition-timing-function: var(--van-ease-out);
}
.van-popover-zoom-leave-active {
    transition-timing-function: var(--van-ease-in);
}
:host,
:root {
    --van-notify-text-color: var(--van-white);
    --van-notify-padding: var(--van-padding-xs) var(--van-padding-md);
    --van-notify-font-size: var(--van-font-size-md);
    --van-notify-line-height: var(--van-line-height-md);
    --van-notify-primary-background: var(--van-primary-color);
    --van-notify-success-background: var(--van-success-color);
    --van-notify-danger-background: var(--van-danger-color);
    --van-notify-warning-background: var(--van-warning-color);
}
.van-notify {
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    padding: var(--van-notify-padding);
    color: var(--van-notify-text-color);
    font-size: var(--van-notify-font-size);
    line-height: var(--van-notify-line-height);
    white-space: pre-wrap;
    text-align: center;
    word-wrap: break-word;
}
.van-notify--primary {
    background: var(--van-notify-primary-background);
}
.van-notify--success {
    background: var(--van-notify-success-background);
}
.van-notify--danger {
    background: var(--van-notify-danger-background);
}
.van-notify--warning {
    background: var(--van-notify-warning-background);
}
:host,
:root {
    --van-dialog-width: 320px;
    --van-dialog-small-screen-width: 90%;
    --van-dialog-font-size: var(--van-font-size-lg);
    --van-dialog-transition: var(--van-duration-base);
    --van-dialog-radius: 16px;
    --van-dialog-background: var(--van-background-2);
    --van-dialog-header-font-weight: var(--van-font-bold);
    --van-dialog-header-line-height: 24px;
    --van-dialog-header-padding-top: 26px;
    --van-dialog-header-isolated-padding: var(--van-padding-lg) 0;
    --van-dialog-message-padding: var(--van-padding-lg);
    --van-dialog-message-font-size: var(--van-font-size-md);
    --van-dialog-message-line-height: var(--van-line-height-md);
    --van-dialog-message-max-height: 60vh;
    --van-dialog-has-title-message-text-color: var(--van-gray-7);
    --van-dialog-has-title-message-padding-top: var(--van-padding-xs);
    --van-dialog-button-height: 48px;
    --van-dialog-round-button-height: 36px;
    --van-dialog-confirm-button-text-color: var(--van-primary-color);
}
.van-dialog {
    top: 45%;
    width: var(--van-dialog-width);
    overflow: hidden;
    font-size: var(--van-dialog-font-size);
    background: var(--van-dialog-background);
    border-radius: var(--van-dialog-radius);
    backface-visibility: hidden;
    transition: var(--van-dialog-transition);
    transition-property: transform, opacity;
}
@media (max-width: 321px) {
    .van-dialog {
        width: var(--van-dialog-small-screen-width);
    }
}
.van-dialog__header {
    color: var(--van-text-color);
    padding-top: var(--van-dialog-header-padding-top);
    font-weight: var(--van-dialog-header-font-weight);
    line-height: var(--van-dialog-header-line-height);
    text-align: center;
}
.van-dialog__header--isolated {
    padding: var(--van-dialog-header-isolated-padding);
}
.van-dialog__content--isolated {
    display: flex;
    align-items: center;
    min-height: 104px;
}
.van-dialog__message {
    color: var(--van-text-color);
    flex: 1;
    max-height: var(--van-dialog-message-max-height);
    padding: 26px var(--van-dialog-message-padding);
    overflow-y: auto;
    font-size: var(--van-dialog-message-font-size);
    line-height: var(--van-dialog-message-line-height);
    white-space: pre-wrap;
    text-align: center;
    word-wrap: break-word;
    -webkit-overflow-scrolling: touch;
}
.van-dialog__message--has-title {
    padding-top: var(--van-dialog-has-title-message-padding-top);
    color: var(--van-dialog-has-title-message-text-color);
}
.van-dialog__message--left {
    text-align: left;
}
.van-dialog__message--right {
    text-align: right;
}
.van-dialog__message--justify {
    text-align: justify;
}
.van-dialog__footer {
    display: flex;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
}
.van-dialog__cancel,
.van-dialog__confirm {
    flex: 1;
    height: var(--van-dialog-button-height);
    margin: 0;
    border: 0;
    border-radius: 0;
}
.van-dialog__confirm,
.van-dialog__confirm:active {
    color: var(--van-dialog-confirm-button-text-color);
}
.van-dialog--round-button .van-dialog__footer {
    position: relative;
    height: auto;
    padding: var(--van-padding-xs) var(--van-padding-lg) var(--van-padding-md);
}
.van-dialog--round-button .van-dialog__message {
    padding-bottom: var(--van-padding-md);
    color: var(--van-text-color);
}
.van-dialog--round-button .van-dialog__cancel,
.van-dialog--round-button .van-dialog__confirm {
    height: var(--van-dialog-round-button-height);
}
.van-dialog--round-button .van-dialog__confirm {
    color: var(--van-white);
}
.van-dialog--round-button .van-action-bar-button--first {
    border-top-left-radius: var(--van-radius-max);
    border-bottom-left-radius: var(--van-radius-max);
}
.van-dialog--round-button .van-action-bar-button--last {
    border-top-right-radius: var(--van-radius-max);
    border-bottom-right-radius: var(--van-radius-max);
}
.van-dialog-bounce-enter-from {
    transform: translate3d(0, -50%, 0) scale(0.7);
    opacity: 0;
}
.van-dialog-bounce-leave-active {
    transform: translate3d(0, -50%, 0) scale(0.9);
    opacity: 0;
}
:host,
:root {
    --van-toast-max-width: 70%;
    --van-toast-font-size: var(--van-font-size-md);
    --van-toast-text-color: var(--van-white);
    --van-toast-loading-icon-color: var(--van-white);
    --van-toast-line-height: var(--van-line-height-md);
    --van-toast-radius: var(--van-radius-lg);
    --van-toast-background: rgba(0, 0, 0, 0.7);
    --van-toast-icon-size: 36px;
    --van-toast-text-min-width: 96px;
    --van-toast-text-padding: var(--van-padding-xs) var(--van-padding-sm);
    --van-toast-default-padding: var(--van-padding-md);
    --van-toast-default-width: 88px;
    --van-toast-default-min-height: 88px;
    --van-toast-position-top-distance: 20%;
    --van-toast-position-bottom-distance: 20%;
}
.van-toast {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-sizing: content-box;
    transition: all var(--van-duration-fast);
    width: var(--van-toast-default-width);
    max-width: var(--van-toast-max-width);
    min-height: var(--van-toast-default-min-height);
    padding: var(--van-toast-default-padding);
    color: var(--van-toast-text-color);
    font-size: var(--van-toast-font-size);
    line-height: var(--van-toast-line-height);
    white-space: pre-wrap;
    word-break: break-all;
    text-align: center;
    background: var(--van-toast-background);
    border-radius: var(--van-toast-radius);
}
.van-toast--break-normal {
    word-break: normal;
    word-wrap: normal;
}
.van-toast--break-word {
    word-break: normal;
    word-wrap: break-word;
}
.van-toast--unclickable {
    overflow: hidden;
    cursor: not-allowed;
}
.van-toast--unclickable * {
    pointer-events: none;
}
.van-toast--html,
.van-toast--text {
    width: -moz-fit-content;
    width: fit-content;
    min-width: var(--van-toast-text-min-width);
    min-height: 0;
    padding: var(--van-toast-text-padding);
}
.van-toast--html .van-toast__text,
.van-toast--text .van-toast__text {
    margin-top: 0;
}
.van-toast--top {
    top: var(--van-toast-position-top-distance);
}
.van-toast--bottom {
    top: auto;
    bottom: var(--van-toast-position-bottom-distance);
}
.van-toast__icon {
    font-size: var(--van-toast-icon-size);
}
.van-toast__loading {
    padding: var(--van-padding-base);
    color: var(--van-toast-loading-icon-color);
}
.van-toast__text {
    margin-top: var(--van-padding-xs);
}
:host,
:root {
    --van-action-sheet-max-height: 80%;
    --van-action-sheet-header-height: 48px;
    --van-action-sheet-header-font-size: var(--van-font-size-lg);
    --van-action-sheet-description-color: var(--van-text-color-2);
    --van-action-sheet-description-font-size: var(--van-font-size-md);
    --van-action-sheet-description-line-height: var(--van-line-height-md);
    --van-action-sheet-item-background: var(--van-background-2);
    --van-action-sheet-item-font-size: var(--van-font-size-lg);
    --van-action-sheet-item-line-height: var(--van-line-height-lg);
    --van-action-sheet-item-text-color: var(--van-text-color);
    --van-action-sheet-item-disabled-text-color: var(--van-text-color-3);
    --van-action-sheet-item-icon-size: 18px;
    --van-action-sheet-item-icon-margin-right: var(--van-padding-xs);
    --van-action-sheet-subname-color: var(--van-text-color-2);
    --van-action-sheet-subname-font-size: var(--van-font-size-sm);
    --van-action-sheet-subname-line-height: var(--van-line-height-sm);
    --van-action-sheet-close-icon-size: 22px;
    --van-action-sheet-close-icon-color: var(--van-gray-5);
    --van-action-sheet-close-icon-padding: 0 var(--van-padding-md);
    --van-action-sheet-cancel-text-color: var(--van-gray-7);
    --van-action-sheet-cancel-padding-top: var(--van-padding-xs);
    --van-action-sheet-cancel-padding-color: var(--van-background);
    --van-action-sheet-loading-icon-size: 22px;
}
.van-action-sheet {
    display: flex;
    flex-direction: column;
    max-height: var(--van-action-sheet-max-height);
    overflow: hidden;
    color: var(--van-action-sheet-item-text-color);
}
.van-action-sheet__content {
    flex: 1 auto;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}
.van-action-sheet__cancel,
.van-action-sheet__item {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 14px var(--van-padding-md);
    font-size: var(--van-action-sheet-item-font-size);
    background: var(--van-action-sheet-item-background);
    border: none;
    cursor: pointer;
}
.van-action-sheet__cancel:active,
.van-action-sheet__item:active {
    background-color: var(--van-active-color);
}
.van-action-sheet__item {
    line-height: var(--van-action-sheet-item-line-height);
}
.van-action-sheet__item--disabled,
.van-action-sheet__item--loading {
    color: var(--van-action-sheet-item-disabled-text-color);
}
.van-action-sheet__item--disabled:active,
.van-action-sheet__item--loading:active {
    background-color: var(--van-action-sheet-item-background);
}
.van-action-sheet__item--disabled {
    cursor: not-allowed;
}
.van-action-sheet__item--loading {
    cursor: default;
}
.van-action-sheet__item-icon {
    font-size: var(--van-action-sheet-item-icon-size);
    margin-right: var(--van-action-sheet-item-icon-margin-right);
}
.van-action-sheet__cancel {
    flex-shrink: 0;
    box-sizing: border-box;
    color: var(--van-action-sheet-cancel-text-color);
}
.van-action-sheet__subname {
    width: 100%;
    margin-top: var(--van-padding-xs);
    color: var(--van-action-sheet-subname-color);
    font-size: var(--van-action-sheet-subname-font-size);
    line-height: var(--van-action-sheet-subname-line-height);
    overflow-wrap: break-word;
}
.van-action-sheet__gap {
    display: block;
    height: var(--van-action-sheet-cancel-padding-top);
    background: var(--van-action-sheet-cancel-padding-color);
}
.van-action-sheet__header {
    flex-shrink: 0;
    font-weight: var(--van-font-bold);
    font-size: var(--van-action-sheet-header-font-size);
    line-height: var(--van-action-sheet-header-height);
    text-align: center;
}
.van-action-sheet__description {
    position: relative;
    flex-shrink: 0;
    padding: 20px var(--van-padding-md);
    color: var(--van-action-sheet-description-color);
    font-size: var(--van-action-sheet-description-font-size);
    line-height: var(--van-action-sheet-description-line-height);
    text-align: center;
}
.van-action-sheet__description:after {
    position: absolute;
    box-sizing: border-box;
    content: " ";
    pointer-events: none;
    right: var(--van-padding-md);
    bottom: 0;
    left: var(--van-padding-md);
    border-bottom: 1px solid var(--van-border-color);
    transform: scaleY(0.5);
}
.van-action-sheet__loading-icon .van-loading__spinner {
    width: var(--van-action-sheet-loading-icon-size);
    height: var(--van-action-sheet-loading-icon-size);
}
.van-action-sheet__close {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 1;
    padding: var(--van-action-sheet-close-icon-padding);
    color: var(--van-action-sheet-close-icon-color);
    font-size: var(--van-action-sheet-close-icon-size);
    line-height: inherit;
}
:host,
:root {
    --van-sticky-z-index: 99;
}
.van-sticky--fixed {
    position: fixed;
    z-index: var(--van-sticky-z-index);
}
:host,
:root {
    --van-swipe-indicator-size: 6px;
    --van-swipe-indicator-margin: var(--van-padding-sm);
    --van-swipe-indicator-active-opacity: 1;
    --van-swipe-indicator-inactive-opacity: 0.3;
    --van-swipe-indicator-active-background: var(--van-primary-color);
    --van-swipe-indicator-inactive-background: var(--van-border-color);
}
.van-swipe {
    position: relative;
    overflow: hidden;
    transform: translateZ(0);
    cursor: grab;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
}
.van-swipe__track {
    display: flex;
    height: 100%;
    transition-property: transform;
}
.van-swipe__track--vertical {
    flex-direction: column;
}
.van-swipe__indicators {
    position: absolute;
    bottom: var(--van-swipe-indicator-margin);
    left: 50%;
    display: flex;
    transform: translate(-50%);
}
.van-swipe__indicators--vertical {
    top: 50%;
    bottom: auto;
    left: var(--van-swipe-indicator-margin);
    flex-direction: column;
    transform: translateY(-50%);
}
.van-swipe__indicators--vertical .van-swipe__indicator:not(:last-child) {
    margin-bottom: var(--van-swipe-indicator-size);
}
.van-swipe__indicator {
    width: var(--van-swipe-indicator-size);
    height: var(--van-swipe-indicator-size);
    background-color: var(--van-swipe-indicator-inactive-background);
    border-radius: 100%;
    opacity: var(--van-swipe-indicator-inactive-opacity);
    transition: opacity var(--van-duration-fast), background-color var(--van-duration-fast);
}
.van-swipe__indicator:not(:last-child) {
    margin-right: var(--van-swipe-indicator-size);
}
.van-swipe__indicator--active {
    background-color: var(--van-swipe-indicator-active-background);
    opacity: var(--van-swipe-indicator-active-opacity);
}
.van-swipe-item {
    position: relative;
    flex-shrink: 0;
    width: 100%;
    height: 100%;
}
:host,
:root {
    --van-image-preview-index-text-color: var(--van-white);
    --van-image-preview-index-font-size: var(--van-font-size-md);
    --van-image-preview-index-line-height: var(--van-line-height-md);
    --van-image-preview-index-text-shadow: 0 1px 1px var(--van-gray-8);
    --van-image-preview-overlay-background: rgba(0, 0, 0, 0.9);
    --van-image-preview-close-icon-size: 22px;
    --van-image-preview-close-icon-color: var(--van-gray-5);
    --van-image-preview-close-icon-margin: var(--van-padding-md);
    --van-image-preview-close-icon-z-index: 1;
}
.van-image-preview {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    max-width: none;
    background-color: transparent;
    transform: none;
}
.van-image-preview__swipe {
    height: 100%;
}
.van-image-preview__swipe-item {
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}
.van-image-preview__cover {
    position: absolute;
    top: 0;
    left: 0;
}
.van-image-preview__image,
.van-image-preview__image-wrap {
    width: 100%;
    transition-property: transform;
}
.van-image-preview__image--vertical,
.van-image-preview__image-wrap--vertical {
    width: auto;
    height: 100%;
}
.van-image-preview__image img,
.van-image-preview__image video,
.van-image-preview__image-wrap img,
.van-image-preview__image-wrap video {
    -webkit-user-drag: none;
}
.van-image-preview__image .van-image__error,
.van-image-preview__image-wrap .van-image__error {
    top: 30%;
    height: 40%;
}
.van-image-preview__image .van-image__error-icon,
.van-image-preview__image-wrap .van-image__error-icon {
    font-size: 36px;
}
.van-image-preview__image .van-image__loading,
.van-image-preview__image-wrap .van-image__loading {
    background-color: transparent;
}
.van-image-preview__index {
    position: absolute;
    top: var(--van-padding-md);
    left: 50%;
    color: var(--van-image-preview-index-text-color);
    font-size: var(--van-image-preview-index-font-size);
    line-height: var(--van-image-preview-index-line-height);
    text-shadow: var(--van-image-preview-index-text-shadow);
    transform: translate(-50%);
}
.van-image-preview__overlay {
    background: var(--van-image-preview-overlay-background);
}
.van-image-preview__close-icon {
    position: absolute;
    z-index: var(--van-image-preview-close-icon-z-index);
    color: var(--van-image-preview-close-icon-color);
    font-size: var(--van-image-preview-close-icon-size);
}
.van-image-preview__close-icon--top-left {
    top: var(--van-image-preview-close-icon-margin);
    left: var(--van-image-preview-close-icon-margin);
}
.van-image-preview__close-icon--top-right {
    top: var(--van-image-preview-close-icon-margin);
    right: var(--van-image-preview-close-icon-margin);
}
.van-image-preview__close-icon--bottom-left {
    bottom: var(--van-image-preview-close-icon-margin);
    left: var(--van-image-preview-close-icon-margin);
}
.van-image-preview__close-icon--bottom-right {
    right: var(--van-image-preview-close-icon-margin);
    bottom: var(--van-image-preview-close-icon-margin);
}
:host,
:root {
    --van-uploader-size: 80px;
    --van-uploader-icon-size: 24px;
    --van-uploader-icon-color: var(--van-gray-4);
    --van-uploader-text-color: var(--van-text-color-2);
    --van-uploader-text-font-size: var(--van-font-size-sm);
    --van-uploader-upload-background: var(--van-gray-1);
    --van-uploader-upload-active-color: var(--van-active-color);
    --van-uploader-delete-color: var(--van-white);
    --van-uploader-delete-icon-size: 14px;
    --van-uploader-delete-background: rgba(0, 0, 0, 0.7);
    --van-uploader-file-background: var(--van-background);
    --van-uploader-file-icon-size: 20px;
    --van-uploader-file-icon-color: var(--van-gray-7);
    --van-uploader-file-name-padding: 0 var(--van-padding-base);
    --van-uploader-file-name-margin-top: var(--van-padding-xs);
    --van-uploader-file-name-font-size: var(--van-font-size-sm);
    --van-uploader-file-name-text-color: var(--van-gray-7);
    --van-uploader-mask-text-color: var(--van-white);
    --van-uploader-mask-background: rgba(50, 50, 51, 0.88);
    --van-uploader-mask-icon-size: 22px;
    --van-uploader-mask-message-font-size: var(--van-font-size-sm);
    --van-uploader-mask-message-line-height: var(--van-line-height-xs);
    --van-uploader-loading-icon-size: 22px;
    --van-uploader-loading-icon-color: var(--van-white);
    --van-uploader-disabled-opacity: var(--van-disabled-opacity);
    --van-uploader-border-radius: 0px;
}
.van-uploader {
    position: relative;
    display: inline-block;
}
.van-uploader__wrapper {
    display: flex;
    flex-wrap: wrap;
}
.van-uploader__wrapper--disabled {
    opacity: var(--van-uploader-disabled-opacity);
}
.van-uploader__input {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    cursor: pointer;
    opacity: 0;
}
.van-uploader__input-wrapper {
    position: relative;
}
.van-uploader__input:disabled {
    cursor: not-allowed;
}
.van-uploader__upload {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    width: var(--van-uploader-size);
    height: var(--van-uploader-size);
    margin: 0 var(--van-padding-xs) var(--van-padding-xs) 0;
    background: var(--van-uploader-upload-background);
    border-radius: var(--van-uploader-border-radius);
}
.van-uploader__upload:active {
    background-color: var(--van-uploader-upload-active-color);
}
.van-uploader__upload--readonly:active {
    background-color: var(--van-uploader-upload-background);
}
.van-uploader__upload-icon {
    color: var(--van-uploader-icon-color);
    font-size: var(--van-uploader-icon-size);
}
.van-uploader__upload-text {
    margin-top: var(--van-padding-xs);
    color: var(--van-uploader-text-color);
    font-size: var(--van-uploader-text-font-size);
}
.van-uploader__preview {
    position: relative;
    margin: 0 var(--van-padding-xs) var(--van-padding-xs) 0;
    cursor: pointer;
}
.van-uploader__preview-image {
    display: block;
    width: var(--van-uploader-size);
    height: var(--van-uploader-size);
    overflow: hidden;
    border-radius: var(--van-uploader-border-radius);
}
.van-uploader__preview-delete {
    position: absolute;
    top: 0;
    right: 0;
}
.van-uploader__preview-delete--shadow {
    width: var(--van-uploader-delete-icon-size);
    height: var(--van-uploader-delete-icon-size);
    background: var(--van-uploader-delete-background);
    border-radius: 0 0 0 12px;
}
.van-uploader__preview-delete-icon {
    position: absolute;
    top: 0;
    right: 0;
    color: var(--van-uploader-delete-color);
    font-size: var(--van-uploader-delete-icon-size);
    transform: scale(0.7) translate(10%, -10%);
}
.van-uploader__mask,
.van-uploader__preview-cover {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
}
.van-uploader__mask {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--van-uploader-mask-text-color);
    background: var(--van-uploader-mask-background);
    border-radius: var(--van-uploader-border-radius);
}
.van-uploader__mask-icon {
    font-size: var(--van-uploader-mask-icon-size);
}
.van-uploader__mask-message {
    margin-top: 6px;
    padding: 0 var(--van-padding-base);
    font-size: var(--van-uploader-mask-message-font-size);
    line-height: var(--van-uploader-mask-message-line-height);
}
.van-uploader__loading {
    width: var(--van-uploader-loading-icon-size);
    height: var(--van-uploader-loading-icon-size);
    color: var(--van-uploader-loading-icon-color);
}
.van-uploader__file {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: var(--van-uploader-size);
    height: var(--van-uploader-size);
    background: var(--van-uploader-file-background);
}
.van-uploader__file-icon {
    color: var(--van-uploader-file-icon-color);
    font-size: var(--van-uploader-file-icon-size);
}
.van-uploader__file-name {
    box-sizing: border-box;
    width: 100%;
    margin-top: var(--van-uploader-file-name-margin-top);
    padding: var(--van-uploader-file-name-padding);
    color: var(--van-uploader-file-name-text-color);
    font-size: var(--van-uploader-file-name-font-size);
    text-align: center;
}
:host,
:root {
    --van-tab-text-color: var(--van-gray-7);
    --van-tab-active-text-color: var(--van-text-color);
    --van-tab-disabled-text-color: var(--van-text-color-3);
    --van-tab-font-size: var(--van-font-size-md);
    --van-tab-line-height: var(--van-line-height-md);
    --van-tabs-default-color: var(--van-primary-color);
    --van-tabs-line-height: 44px;
    --van-tabs-card-height: 30px;
    --van-tabs-nav-background: var(--van-background-2);
    --van-tabs-bottom-bar-width: 40px;
    --van-tabs-bottom-bar-height: 3px;
    --van-tabs-bottom-bar-color: var(--van-primary-color);
}
.van-tab {
    position: relative;
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    padding: 0 var(--van-padding-base);
    color: var(--van-tab-text-color);
    font-size: var(--van-tab-font-size);
    line-height: var(--van-tab-line-height);
    cursor: pointer;
}
.van-tab--active {
    color: var(--van-tab-active-text-color);
    font-weight: var(--van-font-bold);
}
.van-tab--disabled {
    color: var(--van-tab-disabled-text-color);
    cursor: not-allowed;
}
.van-tab--grow {
    flex: 1 0 auto;
    padding: 0 var(--van-padding-sm);
}
.van-tab--shrink {
    flex: none;
    padding: 0 var(--van-padding-xs);
}
.van-tab--card {
    color: var(--van-tabs-default-color);
    border-right: var(--van-border-width) solid var(--van-tabs-default-color);
}
.van-tab--card:last-child {
    border-right: none;
}
.van-tab--card.van-tab--active {
    color: var(--van-white);
    background-color: var(--van-tabs-default-color);
}
.van-tab--card.van-tab--disabled {
    color: var(--van-tab-disabled-text-color);
}
.van-tab__text--ellipsis {
    display: -webkit-box;
    overflow: hidden;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
}
.van-tabs {
    position: relative;
}
.van-tabs__wrap {
    overflow: hidden;
}
.van-tabs__wrap--page-top {
    position: fixed;
}
.van-tabs__wrap--content-bottom {
    top: auto;
    bottom: 0;
}
.van-tabs__nav {
    position: relative;
    display: flex;
    background: var(--van-tabs-nav-background);
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
}
.van-tabs__nav--complete {
    overflow-x: auto;
    overflow-y: hidden;
    -webkit-overflow-scrolling: touch;
}
.van-tabs__nav--complete::-webkit-scrollbar {
    display: none;
}
.van-tabs__nav--line {
    box-sizing: content-box;
    height: 100%;
    padding-bottom: 15px;
}
.van-tabs__nav--line.van-tabs__nav--complete,
.van-tabs__nav--line.van-tabs__nav--shrink {
    padding-right: var(--van-padding-xs);
    padding-left: var(--van-padding-xs);
}
.van-tabs__nav--card {
    box-sizing: border-box;
    height: var(--van-tabs-card-height);
    margin: 0 var(--van-padding-md);
    border: var(--van-border-width) solid var(--van-tabs-default-color);
    border-radius: var(--van-radius-sm);
}
.van-tabs__nav--card.van-tabs__nav--shrink {
    display: inline-flex;
}
.van-tabs__line {
    position: absolute;
    bottom: 15px;
    left: 0;
    z-index: 1;
    width: var(--van-tabs-bottom-bar-width);
    height: var(--van-tabs-bottom-bar-height);
    background: var(--van-tabs-bottom-bar-color);
    border-radius: var(--van-tabs-bottom-bar-height);
}
.van-tabs__track {
    position: relative;
    display: flex;
    width: 100%;
    height: 100%;
    will-change: left;
}
.van-tabs__content--animated {
    overflow: hidden;
}
.van-tabs--line .van-tabs__wrap {
    height: var(--van-tabs-line-height);
}
.van-tabs--card > .van-tabs__wrap {
    height: var(--van-tabs-card-height);
}
.van-tab__panel,
.van-tab__panel-wrapper {
    flex-shrink: 0;
    box-sizing: border-box;
    width: 100%;
}
.van-tab__panel-wrapper--inactive {
    height: 0;
    overflow: visible;
}
:host,
:root {
    --van-cascader-header-height: 48px;
    --van-cascader-header-padding: 0 var(--van-padding-md);
    --van-cascader-title-font-size: var(--van-font-size-lg);
    --van-cascader-title-line-height: 20px;
    --van-cascader-close-icon-size: 22px;
    --van-cascader-close-icon-color: var(--van-gray-5);
    --van-cascader-selected-icon-size: 18px;
    --van-cascader-tabs-height: 48px;
    --van-cascader-active-color: var(--van-primary-color);
    --van-cascader-options-height: 384px;
    --van-cascader-option-disabled-color: var(--van-text-color-3);
    --van-cascader-tab-color: var(--van-text-color);
    --van-cascader-unselected-tab-color: var(--van-text-color-2);
}
.van-cascader__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: var(--van-cascader-header-height);
    padding: var(--van-cascader-header-padding);
}
.van-cascader__title {
    font-weight: var(--van-font-bold);
    font-size: var(--van-cascader-title-font-size);
    line-height: var(--van-cascader-title-line-height);
}
.van-cascader__close-icon {
    color: var(--van-cascader-close-icon-color);
    font-size: var(--van-cascader-close-icon-size);
}
.van-cascader__tabs.van-tabs--line .van-tabs__wrap {
    height: var(--van-cascader-tabs-height);
}
.van-cascader__tab {
    color: var(--van-cascader-tab-color);
    font-weight: var(--van-font-bold);
}
.van-cascader__tab--unselected {
    color: var(--van-cascader-unselected-tab-color);
    font-weight: 400;
}
.van-cascader__option {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px var(--van-padding-md);
    font-size: var(--van-font-size-md);
    line-height: var(--van-line-height-md);
    cursor: pointer;
}
.van-cascader__option:active {
    background-color: var(--van-active-color);
}
.van-cascader__option--selected {
    color: var(--van-cascader-active-color);
    font-weight: var(--van-font-bold);
}
.van-cascader__option--disabled {
    color: var(--van-cascader-option-disabled-color);
    cursor: not-allowed;
}
.van-cascader__option--disabled:active {
    background-color: transparent;
}
.van-cascader__selected-icon {
    font-size: var(--van-cascader-selected-icon-size);
}
.van-cascader__options {
    box-sizing: border-box;
    height: var(--van-cascader-options-height);
    padding-top: 6px;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}
:host,
:root {
    --van-picker-background: var(--van-background-2);
    --van-picker-toolbar-height: 44px;
    --van-picker-title-font-size: var(--van-font-size-lg);
    --van-picker-title-line-height: var(--van-line-height-md);
    --van-picker-action-padding: 0 var(--van-padding-md);
    --van-picker-action-font-size: var(--van-font-size-md);
    --van-picker-confirm-action-color: var(--van-primary-color);
    --van-picker-cancel-action-color: var(--van-text-color-2);
    --van-picker-option-font-size: var(--van-font-size-lg);
    --van-picker-option-padding: 0 var(--van-padding-base);
    --van-picker-option-text-color: var(--van-text-color);
    --van-picker-option-disabled-opacity: 0.3;
    --van-picker-loading-icon-color: var(--van-primary-color);
    --van-picker-loading-mask-color: hsla(0, 0%, 100%, 0.9);
    --van-picker-mask-color: linear-gradient(180deg, hsla(0, 0%, 100%, 0.9), hsla(0, 0%, 100%, 0.4)), linear-gradient(0deg, hsla(0, 0%, 100%, 0.9), hsla(0, 0%, 100%, 0.4));
}
.van-theme-dark {
    --van-picker-loading-mask-color: rgba(0, 0, 0, 0.6);
    --van-picker-mask-color: linear-gradient(180deg, rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.1)), linear-gradient(0deg, rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.1));
}
.van-picker {
    position: relative;
    background: var(--van-picker-background);
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
}
.van-picker__toolbar {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: var(--van-picker-toolbar-height);
}
.van-picker__cancel,
.van-picker__confirm {
    height: 100%;
    padding: var(--van-picker-action-padding);
    font-size: var(--van-picker-action-font-size);
    background-color: transparent;
    border: none;
}
.van-picker__confirm {
    color: var(--van-picker-confirm-action-color);
}
.van-picker__cancel {
    color: var(--van-picker-cancel-action-color);
}
.van-picker__title {
    position: absolute;
    left: 50%;
    color: var(--van-text-color);
    max-width: 50%;
    font-weight: var(--van-font-bold);
    font-size: var(--van-picker-title-font-size);
    line-height: var(--van-picker-title-line-height);
    text-align: center;
    transform: translate(-50%);
}
.van-picker__columns {
    position: relative;
    display: flex;
    cursor: grab;
}
.van-picker__loading {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 3;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--van-picker-loading-icon-color);
    background: var(--van-picker-loading-mask-color);
}
.van-picker__frame {
    top: 50%;
    right: var(--van-padding-md);
    left: var(--van-padding-md);
    z-index: 2;
    transform: translateY(-50%);
}
.van-picker__frame,
.van-picker__mask {
    position: absolute;
    pointer-events: none;
}
.van-picker__mask {
    top: 0;
    left: 0;
    z-index: 1;
    width: 100%;
    height: 100%;
    background-image: var(--van-picker-mask-color);
    background-repeat: no-repeat;
    background-position: top, bottom;
    transform: translateZ(0);
}
.van-picker-column {
    flex: 1;
    overflow: hidden;
    font-size: var(--van-picker-option-font-size);
}
.van-picker-column__wrapper {
    transition-timing-function: cubic-bezier(0.23, 1, 0.68, 1);
}
.van-picker-column__item {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--van-picker-option-padding);
    color: var(--van-picker-option-text-color);
}
.van-picker-column__item--disabled {
    cursor: not-allowed;
    opacity: var(--van-picker-option-disabled-opacity);
}
:host,
:root {
    --van-picker-group-background: var(--van-background-2);
}
.van-picker-group {
    background: var(--van-picker-group-background);
}
.van-picker-group__tabs {
    margin-top: var(--van-padding-base);
}
.van-picker-group__tab-title {
    margin-right: 16px;
}
:host,
:root {
    --van-calendar-background: var(--van-background-2);
    --van-calendar-popup-height: 80%;
    --van-calendar-header-shadow: 0 2px 10px hsla(220, 1%, 50%, 0.16);
    --van-calendar-header-title-height: 44px;
    --van-calendar-header-title-font-size: var(--van-font-size-lg);
    --van-calendar-header-subtitle-font-size: var(--van-font-size-md);
    --van-calendar-header-action-width: 28px;
    --van-calendar-header-action-color: var(--van-text-color);
    --van-calendar-header-action-disabled-color: var(--van-text-color-3);
    --van-calendar-weekdays-height: 30px;
    --van-calendar-weekdays-font-size: var(--van-font-size-sm);
    --van-calendar-month-title-font-size: var(--van-font-size-md);
    --van-calendar-month-mark-color: rgba(242, 243, 245, 0.8);
    --van-calendar-month-mark-font-size: 160px;
    --van-calendar-day-height: 64px;
    --van-calendar-day-font-size: var(--van-font-size-lg);
    --van-calendar-day-margin-bottom: 4px;
    --van-calendar-day-disabled-color: var(--van-text-color-3);
    --van-calendar-range-edge-color: var(--van-white);
    --van-calendar-range-edge-background: var(--van-primary-color);
    --van-calendar-range-middle-color: var(--van-primary-color);
    --van-calendar-range-middle-background-opacity: 0.1;
    --van-calendar-selected-day-size: 54px;
    --van-calendar-selected-day-color: var(--van-white);
    --van-calendar-selected-day-background: var(--van-primary-color);
    --van-calendar-info-font-size: var(--van-font-size-xs);
    --van-calendar-info-line-height: var(--van-line-height-xs);
    --van-calendar-confirm-button-height: 36px;
    --van-calendar-confirm-button-margin: 7px 0;
}
.van-theme-dark {
    --van-calendar-month-mark-color: hsla(210, 1%, 40%, 0.2);
    --van-calendar-day-disabled-color: var(--van-gray-7);
}
.van-calendar {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: var(--van-calendar-background);
}
.van-calendar__popup.van-popup--bottom,
.van-calendar__popup.van-popup--top {
    height: var(--van-calendar-popup-height);
}
.van-calendar__popup.van-popup--left,
.van-calendar__popup.van-popup--right {
    height: 100%;
}
.van-calendar__popup .van-popup__close-icon {
    top: 11px;
}
.van-calendar__header {
    flex-shrink: 0;
    box-shadow: var(--van-calendar-header-shadow);
}
.van-calendar__header-subtitle,
.van-calendar__header-title,
.van-calendar__month-title {
    color: var(--van-text-color);
    height: var(--van-calendar-header-title-height);
    font-weight: var(--van-font-bold);
    line-height: var(--van-calendar-header-title-height);
    text-align: center;
}
.van-calendar__header-title {
    font-size: var(--van-calendar-header-title-font-size);
}
.van-calendar__header-subtitle {
    font-size: var(--van-calendar-header-subtitle-font-size);
}
.van-calendar__header-subtitle--with-swicth {
    display: flex;
    align-items: center;
    padding: 0 var(--van-padding-base);
}
.van-calendar__header-subtitle-text {
    flex: 1;
}
.van-calendar__header-action {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: var(--van-calendar-header-action-width);
    height: 100%;
    color: var(--van-calendar-header-action-color);
    cursor: pointer;
}
.van-calendar__header-action--disabled {
    color: var(--van-calendar-header-action-disabled-color);
    cursor: not-allowed;
}
.van-calendar__month-title {
    font-size: var(--van-calendar-month-title-font-size);
}
.van-calendar__weekdays {
    display: flex;
}
.van-calendar__weekday {
    flex: 1;
    font-size: var(--van-calendar-weekdays-font-size);
    line-height: var(--van-calendar-weekdays-height);
    text-align: center;
}
.van-calendar__body {
    flex: 1;
    overflow: auto;
    -webkit-overflow-scrolling: touch;
}
.van-calendar__days {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
}
.van-calendar__month-mark {
    position: absolute;
    top: 50%;
    left: 50%;
    z-index: 0;
    color: var(--van-calendar-month-mark-color);
    font-size: var(--van-calendar-month-mark-font-size);
    transform: translate(-50%, -50%);
    pointer-events: none;
}
.van-calendar__day,
.van-calendar__selected-day {
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
}
.van-calendar__day {
    position: relative;
    width: 14.285%;
    height: var(--van-calendar-day-height);
    font-size: var(--van-calendar-day-font-size);
    margin-bottom: var(--van-calendar-day-margin-bottom);
    cursor: pointer;
}
.van-calendar__day--end,
.van-calendar__day--multiple-middle,
.van-calendar__day--multiple-selected,
.van-calendar__day--start,
.van-calendar__day--start-end {
    color: var(--van-calendar-range-edge-color);
    background: var(--van-calendar-range-edge-background);
}
.van-calendar__day--start {
    border-radius: var(--van-radius-md) 0 0 var(--van-radius-md);
}
.van-calendar__day--end {
    border-radius: 0 var(--van-radius-md) var(--van-radius-md) 0;
}
.van-calendar__day--multiple-selected,
.van-calendar__day--start-end {
    border-radius: var(--van-radius-md);
}
.van-calendar__day--middle {
    color: var(--van-calendar-range-middle-color);
}
.van-calendar__day--middle:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: currentColor;
    opacity: var(--van-calendar-range-middle-background-opacity);
    content: "";
}
.van-calendar__day--disabled {
    color: var(--van-calendar-day-disabled-color);
    cursor: default;
}
.van-calendar__bottom-info,
.van-calendar__top-info {
    position: absolute;
    right: 0;
    left: 0;
    font-size: var(--van-calendar-info-font-size);
    line-height: var(--van-calendar-info-line-height);
}
@media (max-width: 350px) {
    .van-calendar__bottom-info,
    .van-calendar__top-info {
        font-size: 9px;
    }
}
.van-calendar__top-info {
    top: 6px;
}
.van-calendar__bottom-info {
    bottom: 6px;
}
.van-calendar__selected-day {
    width: var(--van-calendar-selected-day-size);
    height: var(--van-calendar-selected-day-size);
    color: var(--van-calendar-selected-day-color);
    background: var(--van-calendar-selected-day-background);
    border-radius: var(--van-radius-md);
}
.van-calendar__footer {
    flex-shrink: 0;
    padding-left: var(--van-padding-md);
    padding-right: var(--van-padding-md);
}
.van-calendar__confirm {
    height: var(--van-calendar-confirm-button-height);
    margin: var(--van-calendar-confirm-button-margin);
}
:host,
:root {
    --van-address-edit-padding: var(--van-padding-sm);
    --van-address-edit-buttons-padding: var(--van-padding-xl) var(--van-padding-base);
    --van-address-edit-button-margin-bottom: var(--van-padding-sm);
    --van-address-edit-button-font-size: var(--van-font-size-lg);
}
.van-address-edit {
    padding: var(--van-address-edit-padding);
}
.van-address-edit__fields {
    overflow: hidden;
    border-radius: var(--van-padding-xs);
}
.van-address-edit__fields .van-field__label {
    width: 4.1em;
}
.van-address-edit__default {
    margin-top: var(--van-padding-sm);
    overflow: hidden;
    border-radius: var(--van-padding-xs);
}
.van-address-edit__buttons {
    padding: var(--van-address-edit-buttons-padding);
}
.van-address-edit__button {
    margin-bottom: var(--van-address-edit-button-margin-bottom);
    font-size: var(--van-address-edit-button-font-size);
}
.van-address-edit-detail__search-item {
    background: var(--van-gray-2);
}
.van-checkbox-group--horizontal,
.van-radio-group--horizontal {
    display: flex;
    flex-wrap: wrap;
}
:host,
:root {
    --van-checkbox-size: 20px;
    --van-checkbox-border-color: var(--van-gray-5);
    --van-checkbox-duration: var(--van-duration-fast);
    --van-checkbox-label-margin: var(--van-padding-xs);
    --van-checkbox-label-color: var(--van-text-color);
    --van-checkbox-checked-icon-color: var(--van-primary-color);
    --van-checkbox-disabled-icon-color: var(--van-gray-5);
    --van-checkbox-disabled-label-color: var(--van-text-color-3);
    --van-checkbox-disabled-background: var(--van-border-color);
}
.van-checkbox {
    display: flex;
    align-items: center;
    overflow: hidden;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
}
.van-checkbox--disabled {
    cursor: not-allowed;
}
.van-checkbox--label-disabled {
    cursor: default;
}
.van-checkbox--horizontal {
    margin-right: var(--van-padding-sm);
}
.van-checkbox__icon {
    flex: none;
    height: 1em;
    font-size: var(--van-checkbox-size);
    line-height: 1em;
    cursor: pointer;
}
.van-checkbox__icon .van-icon {
    display: block;
    box-sizing: border-box;
    width: 1.25em;
    height: 1.25em;
    color: transparent;
    font-size: 0.8em;
    line-height: 1.25;
    text-align: center;
    border: 1px solid var(--van-checkbox-border-color);
    transition-duration: var(--van-checkbox-duration);
    transition-property: color, border-color, background-color;
}
.van-checkbox__icon--round .van-icon {
    border-radius: 100%;
}
.van-checkbox__icon--indeterminate .van-icon {
    display: flex;
    align-items: center;
    justify-content: center;
}
.van-checkbox__icon--checked .van-icon,
.van-checkbox__icon--indeterminate .van-icon {
    color: var(--van-white);
    border-color: var(--van-checkbox-checked-icon-color);
    background-color: var(--van-checkbox-checked-icon-color);
}
.van-checkbox__icon--disabled {
    cursor: not-allowed;
}
.van-checkbox__icon--disabled .van-icon {
    background-color: var(--van-checkbox-disabled-background);
    border-color: var(--van-checkbox-disabled-icon-color);
}
.van-checkbox__icon--disabled.van-checkbox__icon--checked .van-icon {
    color: var(--van-checkbox-disabled-icon-color);
}
.van-checkbox__label {
    margin-left: var(--van-checkbox-label-margin);
    color: var(--van-checkbox-label-color);
    line-height: var(--van-checkbox-size);
}
.van-checkbox__label--left {
    margin: 0 var(--van-checkbox-label-margin) 0 0;
}
.van-checkbox__label--disabled {
    color: var(--van-checkbox-disabled-label-color);
}
:host,
:root {
    --van-coupon-margin: 0 var(--van-padding-sm) var(--van-padding-sm);
    --van-coupon-content-height: 84px;
    --van-coupon-content-padding: 14px 0;
    --van-coupon-content-text-color: var(--van-text-color);
    --van-coupon-background: var(--van-background-2);
    --van-coupon-active-background: var(--van-active-color);
    --van-coupon-radius: var(--van-radius-lg);
    --van-coupon-shadow: 0 0 4px rgba(0, 0, 0, 0.1);
    --van-coupon-head-width: 96px;
    --van-coupon-amount-color: var(--van-primary-color);
    --van-coupon-amount-font-size: 30px;
    --van-coupon-currency-font-size: 40%;
    --van-coupon-name-font-size: var(--van-font-size-md);
    --van-coupon-disabled-text-color: var(--van-text-color-2);
    --van-coupon-description-padding: var(--van-padding-xs) var(--van-padding-md);
    --van-coupon-description-border-color: var(--van-border-color);
    --van-coupon-checkbox-color: var(--van-primary-color);
}
.van-coupon {
    margin: var(--van-coupon-margin);
    overflow: hidden;
    background: var(--van-coupon-background);
    border-radius: var(--van-coupon-radius);
    box-shadow: var(--van-coupon-shadow);
}
.van-coupon:active {
    background-color: var(--van-coupon-active-background);
}
.van-coupon__content {
    display: flex;
    align-items: center;
    box-sizing: border-box;
    min-height: var(--van-coupon-content-height);
    padding: var(--van-coupon-content-padding);
    color: var(--van-coupon-content-text-color);
}
.van-coupon__head {
    position: relative;
    min-width: var(--van-coupon-head-width);
    padding: 0 var(--van-padding-xs);
    color: var(--van-coupon-amount-color);
    text-align: center;
}
.van-coupon__amount,
.van-coupon__condition,
.van-coupon__name,
.van-coupon__valid {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
.van-coupon__amount {
    margin-bottom: 6px;
    font-weight: var(--van-font-bold);
    font-size: var(--van-coupon-amount-font-size);
}
.van-coupon__amount span {
    font-size: var(--van-coupon-currency-font-size);
}
.van-coupon__amount span:not(:empty) {
    margin-left: 2px;
}
.van-coupon__condition {
    font-size: var(--van-font-size-sm);
    line-height: 16px;
    white-space: pre-wrap;
}
.van-coupon__body {
    position: relative;
    flex: 1;
}
.van-coupon__name {
    margin-bottom: 10px;
    font-weight: var(--van-font-bold);
    font-size: var(--van-coupon-name-font-size);
    line-height: var(--van-line-height-md);
}
.van-coupon__valid {
    font-size: var(--van-font-size-sm);
}
.van-coupon__corner {
    position: absolute;
    top: 0;
    right: var(--van-padding-md);
    bottom: 0;
}
.van-coupon__corner .van-checkbox__icon--checked .van-icon {
    background-color: var(--van-coupon-checkbox-color);
    border-color: var(--van-coupon-checkbox-color);
}
.van-coupon__description {
    padding: var(--van-coupon-description-padding);
    font-size: var(--van-font-size-sm);
    border-top: 1px dashed var(--van-coupon-description-border-color);
}
.van-coupon--disabled:active {
    background-color: var(--van-coupon-background);
}
.van-coupon--disabled .van-coupon-item__content {
    height: calc(var(--van-coupon-content-height) - 10px);
}
.van-coupon--disabled .van-coupon__head {
    color: inherit;
}
:host,
:root {
    --van-radio-size: 20px;
    --van-radio-dot-size: 8px;
    --van-radio-border-color: var(--van-gray-5);
    --van-radio-duration: var(--van-duration-fast);
    --van-radio-label-margin: var(--van-padding-xs);
    --van-radio-label-color: var(--van-text-color);
    --van-radio-checked-icon-color: var(--van-primary-color);
    --van-radio-disabled-icon-color: var(--van-gray-5);
    --van-radio-disabled-label-color: var(--van-text-color-3);
    --van-radio-disabled-background: var(--van-border-color);
}
.van-radio {
    display: flex;
    align-items: center;
    overflow: hidden;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
}
.van-radio--disabled {
    cursor: not-allowed;
}
.van-radio--label-disabled {
    cursor: default;
}
.van-radio--horizontal {
    margin-right: var(--van-padding-sm);
}
.van-radio__icon {
    flex: none;
    height: 1em;
    font-size: var(--van-radio-size);
    line-height: 1em;
    cursor: pointer;
}
.van-radio__icon .van-icon {
    display: block;
    box-sizing: border-box;
    width: 1.25em;
    height: 1.25em;
    color: transparent;
    font-size: 0.8em;
    line-height: 1.25;
    text-align: center;
    border: 1px solid var(--van-radio-border-color);
    transition-duration: var(--van-radio-duration);
    transition-property: color, border-color, background-color;
}
.van-radio__icon--round .van-icon {
    border-radius: 100%;
}
.van-radio__icon--dot {
    position: relative;
    border-radius: 100%;
    box-sizing: border-box;
    width: var(--van-radio-size);
    height: var(--van-radio-size);
    border: 1px solid var(--van-radio-border-color);
    transition-duration: var(--van-radio-duration);
    transition-property: border-color;
}
.van-radio__icon--dot__icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    border-radius: 100%;
    height: calc(100% - var(--van-radio-dot-size));
    width: calc(100% - var(--van-radio-dot-size));
    transition-duration: var(--van-radio-duration);
    transition-property: background-color;
}
.van-radio__icon--checked .van-icon {
    color: var(--van-white);
    background-color: var(--van-radio-checked-icon-color);
}
.van-radio__icon--checked .van-icon,
.van-radio__icon--checked.van-radio__icon--dot {
    border-color: var(--van-radio-checked-icon-color);
}
.van-radio__icon--checked.van-radio__icon--dot .van-radio__icon--dot__icon {
    background: var(--van-radio-checked-icon-color);
}
.van-radio__icon--disabled {
    cursor: not-allowed;
}
.van-radio__icon--disabled .van-icon {
    background-color: var(--van-radio-disabled-background);
    border-color: var(--van-radio-disabled-icon-color);
}
.van-radio__icon--disabled.van-radio__icon--checked .van-icon {
    color: var(--van-radio-disabled-icon-color);
}
.van-radio__label {
    margin-left: var(--van-radio-label-margin);
    color: var(--van-radio-label-color);
    line-height: var(--van-radio-size);
}
.van-radio__label--left {
    margin: 0 var(--van-radio-label-margin) 0 0;
}
.van-radio__label--disabled {
    color: var(--van-radio-disabled-label-color);
}
:host,
:root {
    --van-contact-list-padding: var(--van-padding-sm) var(--van-padding-sm) 80px;
    --van-contact-list-edit-icon-size: 16px;
    --van-contact-list-add-button-z-index: 999;
    --van-contact-list-radio-color: var(--van-primary-color);
    --van-contact-list-item-padding: var(--van-padding-md);
}
.van-contact-list {
    box-sizing: border-box;
    height: 100%;
    padding: var(--van-contact-list-padding);
}
.van-contact-list__item {
    padding: var(--van-contact-list-item-padding);
}
.van-contact-list__item-title {
    display: flex;
    align-items: center;
    padding-right: var(--van-padding-xl);
    padding-left: var(--van-padding-xs);
}
.van-contact-list__item-tag {
    flex: none;
    margin-left: var(--van-padding-xs);
    padding-top: 0;
    padding-bottom: 0;
    line-height: 1.4em;
}
.van-contact-list__group {
    box-sizing: border-box;
    height: 100%;
    overflow-y: scroll;
    -webkit-overflow-scrolling: touch;
    border-radius: var(--van-radius-lg);
}
.van-contact-list__edit {
    font-size: var(--van-contact-list-edit-icon-size);
}
.van-contact-list__radio .van-radio__icon--checked .van-icon {
    background-color: var(--van-contact-list-radio-color);
    border-color: var(--van-contact-list-radio-color);
}
.van-contact-list__bottom {
    position: fixed;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: var(--van-contact-list-add-button-z-index);
    padding-left: var(--van-padding-md);
    padding-right: var(--van-padding-md);
    background-color: var(--van-background-2);
}
.van-contact-list__add {
    height: 40px;
    margin: 5px 0;
}
:host,
:root {
    --van-address-list-padding: var(--van-padding-sm) var(--van-padding-sm) 80px;
    --van-address-list-disabled-text-color: var(--van-text-color-2);
    --van-address-list-disabled-text-padding: calc(var(--van-padding-base) * 5) 0;
    --van-address-list-disabled-text-font-size: var(--van-font-size-md);
    --van-address-list-disabled-text-line-height: var(--van-line-height-md);
    --van-address-list-add-button-z-index: 999;
    --van-address-list-item-padding: var(--van-padding-sm);
    --van-address-list-item-text-color: var(--van-text-color);
    --van-address-list-item-disabled-text-color: var(--van-text-color-3);
    --van-address-list-item-font-size: 13px;
    --van-address-list-item-line-height: var(--van-line-height-sm);
    --van-address-list-radio-color: var(--van-primary-color);
    --van-address-list-edit-icon-size: 20px;
}
.van-address-list {
    box-sizing: border-box;
    height: 100%;
    padding: var(--van-address-list-padding);
}
.van-address-list__bottom {
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: var(--van-address-list-add-button-z-index);
    box-sizing: border-box;
    width: 100%;
    padding-left: var(--van-padding-md);
    padding-right: var(--van-padding-md);
    background-color: var(--van-background-2);
}
.van-address-list__add {
    height: 40px;
    margin: 5px 0;
}
.van-address-list__disabled-text {
    padding: var(--van-address-list-disabled-text-padding);
    color: var(--van-address-list-disabled-text-color);
    font-size: var(--van-address-list-disabled-text-font-size);
    line-height: var(--van-address-list-disabled-text-line-height);
}
.van-address-item {
    padding: var(--van-address-list-item-padding);
    background-color: var(--van-background-2);
    border-radius: var(--van-radius-lg);
}
.van-address-item:not(:last-child) {
    margin-bottom: var(--van-padding-sm);
}
.van-address-item__title {
    padding-right: 44px;
}
.van-address-item__name {
    display: flex;
    align-items: center;
    margin-bottom: var(--van-padding-xs);
    font-size: var(--van-font-size-lg);
    line-height: var(--van-line-height-lg);
}
.van-address-item__tag {
    flex: none;
    margin-left: var(--van-padding-xs);
    padding-top: 0;
    padding-bottom: 0;
    line-height: 1.4em;
}
.van-address-item__address {
    color: var(--van-address-list-item-text-color);
    font-size: var(--van-address-list-item-font-size);
    line-height: var(--van-address-list-item-line-height);
}
.van-address-item--disabled .van-address-item__address,
.van-address-item--disabled .van-address-item__name {
    color: var(--van-address-list-item-disabled-text-color);
}
.van-address-item__edit {
    position: absolute;
    top: 50%;
    right: var(--van-padding-md);
    color: var(--van-gray-6);
    font-size: var(--van-address-list-edit-icon-size);
    transform: translateY(-50%);
}
.van-address-item .van-cell {
    padding: 0;
}
.van-address-item .van-radio__label {
    margin-left: var(--van-padding-sm);
}
.van-address-item .van-radio__icon--checked .van-icon {
    background-color: var(--van-address-list-radio-color);
    border-color: var(--van-address-list-radio-color);
}
:host,
:root {
    --van-barrage-font-size: 16px;
    --van-barrage-space: 10px;
    --van-barrage-font: inherit;
    --van-barrage-color: var(--van-white);
}
.van-barrage {
    position: relative;
    overflow: hidden;
}
.van-barrage__item {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 99;
    padding-bottom: var(--van-barrage-space);
    opacity: 0.75;
    line-height: 1;
    font-size: var(--van-barrage-font-size);
    font-family: var(--van-barrage-font);
    font-weight: 700;
    white-space: nowrap;
    color: var(--van-barrage-color);
    text-shadow: 1px 0 1px #000, 0 1px 1px #000, 0 -1px 1px #000, -1px 0 1px #000;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    will-change: transform;
    transform: translate(110%);
}
@keyframes van-barrage {
    0% {
        transform: translate(110%);
    }
    to {
        transform: translate(var(--move-distance));
    }
}
:host,
:root {
    --van-cell-group-background: var(--van-background-2);
    --van-cell-group-title-color: var(--van-text-color-2);
    --van-cell-group-title-padding: var(--van-padding-md) var(--van-padding-md);
    --van-cell-group-title-font-size: var(--van-font-size-md);
    --van-cell-group-title-line-height: 16px;
    --van-cell-group-inset-padding: 0 var(--van-padding-md);
    --van-cell-group-inset-radius: var(--van-radius-lg);
    --van-cell-group-inset-title-padding: var(--van-padding-md) var(--van-padding-md);
}
.van-cell-group {
    background: var(--van-cell-group-background);
}
.van-cell-group--inset {
    margin: var(--van-cell-group-inset-padding);
    border-radius: var(--van-cell-group-inset-radius);
    overflow: hidden;
}
.van-cell-group__title {
    padding: var(--van-cell-group-title-padding);
    color: var(--van-cell-group-title-color);
    font-size: var(--van-cell-group-title-font-size);
    line-height: var(--van-cell-group-title-line-height);
}
.van-cell-group__title--inset {
    padding: var(--van-cell-group-inset-title-padding);
}
:host,
:root {
    --van-circle-size: 100px;
    --van-circle-color: var(--van-primary-color);
    --van-circle-layer-color: var(--van-white);
    --van-circle-text-color: var(--van-text-color);
    --van-circle-text-font-weight: var(--van-font-bold);
    --van-circle-text-font-size: var(--van-font-size-md);
    --van-circle-text-line-height: var(--van-line-height-md);
}
.van-circle {
    position: relative;
    display: inline-block;
    width: var(--van-circle-size);
    height: var(--van-circle-size);
    text-align: center;
}
.van-circle svg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}
.van-circle__layer {
    stroke: var(--van-circle-layer-color);
}
.van-circle__hover {
    fill: none;
    stroke: var(--van-circle-color);
    stroke-linecap: round;
}
.van-circle__text {
    position: absolute;
    top: 50%;
    left: 0;
    box-sizing: border-box;
    width: 100%;
    padding: 0 var(--van-padding-base);
    color: var(--van-circle-text-color);
    font-weight: var(--van-circle-text-font-weight);
    font-size: var(--van-circle-text-font-size);
    line-height: var(--van-circle-text-line-height);
    transform: translateY(-50%);
}
.van-row {
    display: flex;
    flex-wrap: wrap;
}
.van-row--nowrap {
    flex-wrap: nowrap;
}
.van-row--justify-center {
    justify-content: center;
}
.van-row--justify-end {
    justify-content: flex-end;
}
.van-row--justify-space-between {
    justify-content: space-between;
}
.van-row--justify-space-around {
    justify-content: space-around;
}
.van-row--align-center {
    align-items: center;
}
.van-row--align-bottom {
    align-items: flex-end;
}
.van-col {
    display: block;
    box-sizing: border-box;
    min-height: 1px;
}
.van-col--1 {
    flex: 0 0 4.16666667%;
    max-width: 4.16666667%;
}
.van-col--offset-1 {
    margin-left: 4.16666667%;
}
.van-col--2 {
    flex: 0 0 8.33333333%;
    max-width: 8.33333333%;
}
.van-col--offset-2 {
    margin-left: 8.33333333%;
}
.van-col--3 {
    flex: 0 0 12.5%;
    max-width: 12.5%;
}
.van-col--offset-3 {
    margin-left: 12.5%;
}
.van-col--4 {
    flex: 0 0 16.66666667%;
    max-width: 16.66666667%;
}
.van-col--offset-4 {
    margin-left: 16.66666667%;
}
.van-col--5 {
    flex: 0 0 20.83333333%;
    max-width: 20.83333333%;
}
.van-col--offset-5 {
    margin-left: 20.83333333%;
}
.van-col--6 {
    flex: 0 0 25%;
    max-width: 25%;
}
.van-col--offset-6 {
    margin-left: 25%;
}
.van-col--7 {
    flex: 0 0 29.16666667%;
    max-width: 29.16666667%;
}
.van-col--offset-7 {
    margin-left: 29.16666667%;
}
.van-col--8 {
    flex: 0 0 33.33333333%;
    max-width: 33.33333333%;
}
.van-col--offset-8 {
    margin-left: 33.33333333%;
}
.van-col--9 {
    flex: 0 0 37.5%;
    max-width: 37.5%;
}
.van-col--offset-9 {
    margin-left: 37.5%;
}
.van-col--10 {
    flex: 0 0 41.66666667%;
    max-width: 41.66666667%;
}
.van-col--offset-10 {
    margin-left: 41.66666667%;
}
.van-col--11 {
    flex: 0 0 45.83333333%;
    max-width: 45.83333333%;
}
.van-col--offset-11 {
    margin-left: 45.83333333%;
}
.van-col--12 {
    flex: 0 0 50%;
    max-width: 50%;
}
.van-col--offset-12 {
    margin-left: 50%;
}
.van-col--13 {
    flex: 0 0 54.16666667%;
    max-width: 54.16666667%;
}
.van-col--offset-13 {
    margin-left: 54.16666667%;
}
.van-col--14 {
    flex: 0 0 58.33333333%;
    max-width: 58.33333333%;
}
.van-col--offset-14 {
    margin-left: 58.33333333%;
}
.van-col--15 {
    flex: 0 0 62.5%;
    max-width: 62.5%;
}
.van-col--offset-15 {
    margin-left: 62.5%;
}
.van-col--16 {
    flex: 0 0 66.66666667%;
    max-width: 66.66666667%;
}
.van-col--offset-16 {
    margin-left: 66.66666667%;
}
.van-col--17 {
    flex: 0 0 70.83333333%;
    max-width: 70.83333333%;
}
.van-col--offset-17 {
    margin-left: 70.83333333%;
}
.van-col--18 {
    flex: 0 0 75%;
    max-width: 75%;
}
.van-col--offset-18 {
    margin-left: 75%;
}
.van-col--19 {
    flex: 0 0 79.16666667%;
    max-width: 79.16666667%;
}
.van-col--offset-19 {
    margin-left: 79.16666667%;
}
.van-col--20 {
    flex: 0 0 83.33333333%;
    max-width: 83.33333333%;
}
.van-col--offset-20 {
    margin-left: 83.33333333%;
}
.van-col--21 {
    flex: 0 0 87.5%;
    max-width: 87.5%;
}
.van-col--offset-21 {
    margin-left: 87.5%;
}
.van-col--22 {
    flex: 0 0 91.66666667%;
    max-width: 91.66666667%;
}
.van-col--offset-22 {
    margin-left: 91.66666667%;
}
.van-col--23 {
    flex: 0 0 95.83333333%;
    max-width: 95.83333333%;
}
.van-col--offset-23 {
    margin-left: 95.83333333%;
}
.van-col--24 {
    flex: 0 0 100%;
    max-width: 100%;
}
.van-col--offset-24 {
    margin-left: 100%;
}
:host,
:root {
    --van-count-down-text-color: var(--van-text-color);
    --van-count-down-font-size: var(--van-font-size-md);
    --van-count-down-line-height: var(--van-line-height-md);
}
.van-count-down {
    color: var(--van-count-down-text-color);
    font-size: var(--van-count-down-font-size);
    line-height: var(--van-count-down-line-height);
}
:host,
:root {
    --van-empty-padding: var(--van-padding-xl) 0;
    --van-empty-image-size: 160px;
    --van-empty-description-margin-top: var(--van-padding-md);
    --van-empty-description-padding: 0 60px;
    --van-empty-description-color: var(--van-text-color-2);
    --van-empty-description-font-size: var(--van-font-size-md);
    --van-empty-description-line-height: var(--van-line-height-md);
    --van-empty-bottom-margin-top: 24px;
}
.van-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    padding: var(--van-empty-padding);
}
.van-empty__image {
    width: var(--van-empty-image-size);
    height: var(--van-empty-image-size);
}
.van-empty__image img {
    width: 100%;
    height: 100%;
}
.van-empty__description {
    margin-top: var(--van-empty-description-margin-top);
    padding: var(--van-empty-description-padding);
    color: var(--van-empty-description-color);
    font-size: var(--van-empty-description-font-size);
    line-height: var(--van-empty-description-line-height);
}
.van-empty__bottom {
    margin-top: var(--van-empty-bottom-margin-top);
}
.van-theme-dark .van-empty {
    opacity: 0.5;
}
:host,
:root {
    --van-coupon-list-background: var(--van-background);
    --van-coupon-list-field-padding: 5px 0 5px var(--van-padding-md);
    --van-coupon-list-exchange-button-height: 32px;
    --van-coupon-list-close-button-height: 40px;
    --van-coupon-list-empty-tip-color: var(--van-text-color-2);
    --van-coupon-list-empty-tip-font-size: var(--van-font-size-md);
    --van-coupon-list-empty-tip-line-height: var(--van-line-height-md);
}
.van-coupon-list {
    position: relative;
    height: 100%;
    background: var(--van-coupon-list-background);
}
.van-coupon-list__field {
    padding: var(--van-coupon-list-field-padding);
}
.van-coupon-list__field .van-field__body {
    height: 34px;
    padding-left: var(--van-padding-sm);
    line-height: 34px;
    background: var(--van-background);
    border-radius: var(--van-radius-max);
}
.van-coupon-list__field .van-field__body::-moz-placeholder {
    color: var(--van-text-color-3);
}
.van-coupon-list__field .van-field__body::placeholder {
    color: var(--van-text-color-3);
}
.van-coupon-list__field .van-field__clear {
    margin-right: 0;
}
.van-coupon-list__exchange-bar {
    display: flex;
    align-items: center;
    background-color: var(--van-background-2);
}
.van-coupon-list__exchange {
    flex: none;
    height: var(--van-coupon-list-exchange-button-height);
    font-size: var(--van-font-size-lg);
    line-height: calc(var(--van-coupon-list-exchange-button-height) - 2px);
    border: 0;
}
.van-coupon-list .van-tabs__wrap {
    box-shadow: 0 6px 12px -12px var(--van-gray-6);
}
.van-coupon-list__list {
    box-sizing: border-box;
    padding: var(--van-padding-md) 0 var(--van-padding-lg);
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}
.van-coupon-list__list--with-bottom {
    padding-bottom: 50px;
}
.van-coupon-list__bottom {
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: 999;
    box-sizing: border-box;
    width: 100%;
    padding: 5px var(--van-padding-md);
    font-weight: var(--van-font-bold);
    background-color: var(--van-background-2);
}
.van-coupon-list__close {
    height: var(--van-coupon-list-close-button-height);
}
.van-coupon-list__empty-tip {
    color: var(--van-coupon-list-empty-tip-color);
    font-size: var(--van-coupon-list-empty-tip-font-size);
    line-height: var(--van-coupon-list-empty-tip-line-height);
}
:host,
:root {
    --van-divider-margin: var(--van-padding-md) 0;
    --van-divider-vertical-margin: 0 var(--van-padding-xs);
    --van-divider-text-color: var(--van-text-color-2);
    --van-divider-font-size: var(--van-font-size-md);
    --van-divider-line-height: 24px;
    --van-divider-border-color: var(--van-border-color);
    --van-divider-content-padding: var(--van-padding-md);
    --van-divider-content-left-width: 10%;
    --van-divider-content-right-width: 10%;
}
.van-divider {
    display: flex;
    align-items: center;
    margin: var(--van-divider-margin);
    color: var(--van-divider-text-color);
    font-size: var(--van-divider-font-size);
    line-height: var(--van-divider-line-height);
    border-color: var(--van-divider-border-color);
    border-style: solid;
    border-width: 0;
}
.van-divider:after,
.van-divider:before {
    display: block;
    flex: 1;
    box-sizing: border-box;
    height: 1px;
    border-color: inherit;
    border-style: inherit;
    border-width: var(--van-border-width) 0 0;
}
.van-divider:before {
    content: "";
}
.van-divider--hairline:after,
.van-divider--hairline:before {
    transform: scaleY(0.5);
}
.van-divider--dashed {
    border-style: dashed;
}
.van-divider--content-center:before,
.van-divider--content-left:before,
.van-divider--content-right:before {
    margin-right: var(--van-divider-content-padding);
}
.van-divider--content-center:after,
.van-divider--content-left:after,
.van-divider--content-right:after {
    margin-left: var(--van-divider-content-padding);
    content: "";
}
.van-divider--content-left:before {
    max-width: var(--van-divider-content-left-width);
}
.van-divider--content-right:after {
    max-width: var(--van-divider-content-right-width);
}
.van-divider--vertical {
    display: inline-block;
    width: var(--van-border-width);
    height: 1em;
    margin: var(--van-divider-vertical-margin);
    vertical-align: middle;
}
.van-divider--vertical:before {
    height: 100%;
    border-width: 0 0 0 var(--van-border-width);
}
.van-divider--vertical:after {
    display: none;
}
.van-divider--vertical.van-divider--hairline:before {
    transform: scaleX(0.5);
}
:host,
:root {
    --van-dropdown-menu-height: 48px;
    --van-dropdown-menu-background: var(--van-background-2);
    --van-dropdown-menu-shadow: 0 2px 12px hsla(210, 1%, 40%, 0.12);
    --van-dropdown-menu-title-font-size: 15px;
    --van-dropdown-menu-title-text-color: var(--van-text-color);
    --van-dropdown-menu-title-active-text-color: var(--van-primary-color);
    --van-dropdown-menu-title-disabled-text-color: var(--van-text-color-2);
    --van-dropdown-menu-title-padding: 0 var(--van-padding-xs);
    --van-dropdown-menu-title-line-height: var(--van-line-height-lg);
    --van-dropdown-menu-option-active-color: var(--van-primary-color);
    --van-dropdown-menu-option-disabled-color: var(--van-text-color-3);
    --van-dropdown-menu-content-max-height: 80%;
}
.van-dropdown-menu {
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
}
.van-dropdown-menu__bar {
    position: relative;
    display: flex;
    height: var(--van-dropdown-menu-height);
    background: var(--van-dropdown-menu-background);
    box-shadow: var(--van-dropdown-menu-shadow);
}
.van-dropdown-menu__bar--opened {
    z-index: calc(var(--van-dropdown-item-z-index) + 1);
}
.van-dropdown-menu__bar--scrollable {
    padding-left: var(--van-padding-base);
    padding-right: var(--van-padding-xs);
    overflow-x: auto;
    overflow-y: hidden;
    -webkit-overflow-scrolling: touch;
}
.van-dropdown-menu__bar--scrollable::-webkit-scrollbar {
    display: none;
}
.van-dropdown-menu__item {
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: center;
    min-width: 0;
}
.van-dropdown-menu__item--disabled .van-dropdown-menu__title {
    color: var(--van-dropdown-menu-title-disabled-text-color);
}
.van-dropdown-menu__item--grow {
    flex: 1 0 auto;
    padding-left: var(--van-padding-base);
    padding-right: var(--van-padding-sm);
}
.van-dropdown-menu__title {
    position: relative;
    box-sizing: border-box;
    max-width: 100%;
    padding: var(--van-dropdown-menu-title-padding);
    color: var(--van-dropdown-menu-title-text-color);
    font-size: var(--van-dropdown-menu-title-font-size);
    line-height: var(--van-dropdown-menu-title-line-height);
}
.van-dropdown-menu__title:after {
    position: absolute;
    top: 50%;
    right: -4px;
    margin-top: -5px;
    border: 3px solid;
    border-color: transparent transparent var(--van-gray-4) var(--van-gray-4);
    transform: rotate(-45deg);
    opacity: 0.8;
    content: "";
}
.van-dropdown-menu__title--active {
    color: var(--van-dropdown-menu-title-active-text-color);
}
.van-dropdown-menu__title--active:after {
    border-color: transparent transparent currentColor currentColor;
}
.van-dropdown-menu__title--down:after {
    margin-top: -1px;
    transform: rotate(135deg);
}
:host,
:root {
    --van-dropdown-item-z-index: 10;
}
.van-dropdown-item {
    position: fixed;
    right: 0;
    left: 0;
    z-index: var(--van-dropdown-item-z-index);
    overflow: hidden;
}
.van-dropdown-item__icon {
    display: block;
    line-height: inherit;
}
.van-dropdown-item__option {
    text-align: left;
}
.van-dropdown-item__option--active,
.van-dropdown-item__option--active .van-dropdown-item__icon {
    color: var(--van-dropdown-menu-option-active-color);
}
.van-dropdown-item__option--disabled,
.van-dropdown-item__option--disabled .van-dropdown-item__icon {
    color: var(--van-dropdown-menu-option-disabled-color);
}
.van-dropdown-item--up {
    top: 0;
}
.van-dropdown-item--down {
    bottom: 0;
}
.van-dropdown-item__content {
    position: absolute;
    max-height: var(--van-dropdown-menu-content-max-height);
}
:host,
:root {
    --van-floating-panel-border-radius: 16px;
    --van-floating-panel-header-height: 30px;
    --van-floating-panel-z-index: 999;
    --van-floating-panel-background: var(--van-background-2);
    --van-floating-panel-bar-width: 20px;
    --van-floating-panel-bar-height: 3px;
    --van-floating-panel-bar-color: var(--van-gray-5);
}
.van-floating-panel {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100vw;
    z-index: var(--van-floating-panel-z-index);
    display: flex;
    flex-direction: column;
    touch-action: none;
    border-top-left-radius: var(--van-floating-panel-border-radius);
    border-top-right-radius: var(--van-floating-panel-border-radius);
    background: var(--van-floating-panel-background);
    will-change: transform;
}
.van-floating-panel:after {
    content: "";
    display: block;
    position: absolute;
    bottom: -100vh;
    height: 100vh;
    width: 100vw;
    background-color: inherit;
}
.van-floating-panel__header {
    height: var(--van-floating-panel-header-height);
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: grab;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
}
.van-floating-panel__header-bar {
    height: var(--van-floating-panel-bar-height);
    width: var(--van-floating-panel-bar-width);
    border-radius: var(--van-radius-md);
    background: var(--van-floating-panel-bar-color);
}
.van-floating-panel__content {
    flex: 1;
    overflow-y: auto;
    background-color: var(--van-floating-panel-background);
}
.van-grid {
    display: flex;
    flex-wrap: wrap;
}
:host,
:root {
    --van-grid-item-content-padding: var(--van-padding-md) var(--van-padding-xs);
    --van-grid-item-content-background: var(--van-background-2);
    --van-grid-item-content-active-color: var(--van-active-color);
    --van-grid-item-icon-size: 28px;
    --van-grid-item-text-color: var(--van-text-color);
    --van-grid-item-text-font-size: var(--van-font-size-sm);
}
.van-grid-item {
    position: relative;
    box-sizing: border-box;
}
.van-grid-item--square {
    height: 0;
}
.van-grid-item__icon {
    font-size: var(--van-grid-item-icon-size);
}
.van-grid-item__text {
    color: var(--van-grid-item-text-color);
    font-size: var(--van-grid-item-text-font-size);
    line-height: 1.5;
    word-break: break-all;
}
.van-grid-item__icon + .van-grid-item__text {
    margin-top: var(--van-padding-xs);
}
.van-grid-item__content {
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    height: 100%;
    padding: var(--van-grid-item-content-padding);
    background: var(--van-grid-item-content-background);
}
.van-grid-item__content:after {
    z-index: 1;
    border-width: 0 var(--van-border-width) var(--van-border-width) 0;
}
.van-grid-item__content--square {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
}
.van-grid-item__content--center {
    align-items: center;
    justify-content: center;
}
.van-grid-item__content--horizontal {
    flex-direction: row;
}
.van-grid-item__content--horizontal .van-grid-item__text {
    margin: 0 0 0 var(--van-padding-xs);
}
.van-grid-item__content--reverse {
    flex-direction: column-reverse;
}
.van-grid-item__content--reverse .van-grid-item__text {
    margin: 0 0 var(--van-padding-xs);
}
.van-grid-item__content--horizontal.van-grid-item__content--reverse {
    flex-direction: row-reverse;
}
.van-grid-item__content--horizontal.van-grid-item__content--reverse .van-grid-item__text {
    margin: 0 var(--van-padding-xs) 0 0;
}
.van-grid-item__content--surround:after {
    border-width: var(--van-border-width);
}
.van-grid-item__content--clickable {
    cursor: pointer;
}
.van-grid-item__content--clickable:active {
    background-color: var(--van-grid-item-content-active-color);
}
:host,
:root {
    --van-highlight-tag-color: var(--van-primary-color);
}
.van-highlight__tag {
    color: var(--van-highlight-tag-color);
}
:host,
:root {
    --van-index-bar-sidebar-z-index: 2;
    --van-index-bar-index-font-size: var(--van-font-size-xs);
    --van-index-bar-index-line-height: var(--van-line-height-xs);
    --van-index-bar-index-active-color: var(--van-primary-color);
}
.van-index-bar__sidebar {
    position: fixed;
    top: 50%;
    right: 0;
    z-index: var(--van-index-bar-sidebar-z-index);
    display: flex;
    flex-direction: column;
    text-align: center;
    transform: translateY(-50%);
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
}
.van-index-bar__index {
    padding: 0 var(--van-padding-xs) 0 var(--van-padding-md);
    font-weight: var(--van-font-bold);
    font-size: var(--van-index-bar-index-font-size);
    line-height: var(--van-index-bar-index-line-height);
}
.van-index-bar__index--active {
    color: var(--van-index-bar-index-active-color);
    font-weight: 700;
}
:host,
:root {
    --van-index-anchor-z-index: 1;
    --van-index-anchor-padding: 0 var(--van-padding-md);
    --van-index-anchor-text-color: var(--van-text-color);
    --van-index-anchor-font-weight: var(--van-font-bold);
    --van-index-anchor-font-size: var(--van-font-size-md);
    --van-index-anchor-line-height: 32px;
    --van-index-anchor-background: transparent;
    --van-index-anchor-sticky-text-color: var(--van-primary-color);
    --van-index-anchor-sticky-background: var(--van-background-2);
}
.van-index-anchor {
    z-index: var(--van-index-anchor-z-index);
    box-sizing: border-box;
    padding: var(--van-index-anchor-padding);
    color: var(--van-index-anchor-text-color);
    font-weight: var(--van-index-anchor-font-weight);
    font-size: var(--van-index-anchor-font-size);
    line-height: var(--van-index-anchor-line-height);
    background: var(--van-index-anchor-background);
}
.van-index-anchor--sticky {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    color: var(--van-index-anchor-sticky-text-color);
    background: var(--van-index-anchor-sticky-background);
}
:host,
:root {
    --van-pagination-height: 40px;
    --van-pagination-font-size: var(--van-font-size-md);
    --van-pagination-item-width: 36px;
    --van-pagination-item-default-color: var(--van-primary-color);
    --van-pagination-item-disabled-color: var(--van-gray-7);
    --van-pagination-item-disabled-background: var(--van-background);
    --van-pagination-background: var(--van-background-2);
    --van-pagination-desc-color: var(--van-gray-7);
    --van-pagination-disabled-opacity: var(--van-disabled-opacity);
}
.van-pagination {
    font-size: var(--van-pagination-font-size);
}
.van-pagination__items {
    display: flex;
}
.van-pagination__item,
.van-pagination__page-desc {
    display: flex;
    align-items: center;
    justify-content: center;
}
.van-pagination__item {
    flex: 1;
    box-sizing: border-box;
    min-width: var(--van-pagination-item-width);
    height: var(--van-pagination-height);
    color: var(--van-pagination-item-default-color);
    background: var(--van-pagination-background);
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
}
.van-pagination__item button {
    flex: 1;
    height: 100%;
    border: none;
    padding: 0;
    background: transparent;
}
.van-pagination__item button[disabled] {
    cursor: not-allowed;
}
.van-pagination__item:active {
    color: var(--van-white);
    background-color: var(--van-pagination-item-default-color);
}
.van-pagination__item:not(:last-child):after {
    border-right-width: 0;
}
.van-pagination__item--active {
    color: var(--van-white);
    background-color: var(--van-pagination-item-default-color);
}
.van-pagination__item--page {
    flex-grow: 0;
}
.van-pagination__item--next,
.van-pagination__item--prev {
    padding: 0 var(--van-padding-base);
    cursor: pointer;
}
.van-pagination__item--border:first-child:after {
    border-right-width: var(--van-border-width);
}
.van-pagination__item--disabled,
.van-pagination__item--disabled:active {
    color: var(--van-pagination-item-disabled-color);
    background-color: var(--van-pagination-item-disabled-background);
    opacity: var(--van-pagination-disabled-opacity);
}
.van-pagination__page-desc {
    flex: 1;
    height: var(--van-pagination-height);
    color: var(--van-pagination-desc-color);
}
:host,
:root {
    --van-password-input-height: 50px;
    --van-password-input-margin: 0 var(--van-padding-md);
    --van-password-input-font-size: 20px;
    --van-password-input-radius: 6px;
    --van-password-input-background: var(--van-background-2);
    --van-password-input-info-color: var(--van-text-color-2);
    --van-password-input-info-font-size: var(--van-font-size-md);
    --van-password-input-error-info-color: var(--van-danger-color);
    --van-password-input-dot-size: 10px;
    --van-password-input-dot-color: var(--van-text-color);
    --van-password-input-text-color: var(--van-text-color);
    --van-password-input-cursor-color: var(--van-text-color);
    --van-password-input-cursor-width: 1px;
    --van-password-input-cursor-height: 40%;
    --van-password-input-cursor-duration: 1s;
}
.van-password-input {
    position: relative;
    margin: var(--van-password-input-margin);
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
}
.van-password-input__error-info,
.van-password-input__info {
    margin-top: var(--van-padding-md);
    font-size: var(--van-password-input-info-font-size);
    text-align: center;
}
.van-password-input__info {
    color: var(--van-password-input-info-color);
}
.van-password-input__error-info {
    color: var(--van-password-input-error-info-color);
}
.van-password-input__security {
    display: flex;
    width: 100%;
    height: var(--van-password-input-height);
    cursor: pointer;
}
.van-password-input__security:after {
    border-radius: var(--van-password-input-radius);
}
.van-password-input__security li {
    position: relative;
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--van-password-input-text-color);
    font-size: var(--van-password-input-font-size);
    line-height: 1.2;
    background: var(--van-password-input-background);
}
.van-password-input__security i {
    width: var(--van-password-input-dot-size);
    height: var(--van-password-input-dot-size);
    background: var(--van-password-input-dot-color);
    border-radius: 100%;
    visibility: hidden;
}
.van-password-input__cursor,
.van-password-input__security i {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}
.van-password-input__cursor {
    width: var(--van-password-input-cursor-width);
    height: var(--van-password-input-cursor-height);
    background: var(--van-password-input-cursor-color);
    animation: var(--van-password-input-cursor-duration) van-cursor-flicker infinite;
}
@keyframes van-cursor-flicker {
    0% {
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}
:host,
:root {
    --van-progress-height: 4px;
    --van-progress-color: var(--van-primary-color);
    --van-progress-inactive-color: var(--van-gray-5);
    --van-progress-background: var(--van-gray-3);
    --van-progress-pivot-padding: 0 5px;
    --van-progress-pivot-text-color: var(--van-white);
    --van-progress-pivot-font-size: var(--van-font-size-xs);
    --van-progress-pivot-line-height: 1.6;
    --van-progress-pivot-background: var(--van-primary-color);
}
.van-progress {
    position: relative;
    height: var(--van-progress-height);
    background: var(--van-progress-background);
    border-radius: var(--van-progress-height);
}
.van-progress__portion {
    position: absolute;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--van-progress-color);
    border-radius: inherit;
    transform-origin: 0;
    transition: all var(--van-duration-base) var(--van-ease-out);
}
.van-progress__portion--inactive {
    background: var(--van-progress-inactive-color);
}
.van-progress__pivot {
    position: absolute;
    top: 50%;
    box-sizing: border-box;
    min-width: 3.6em;
    padding: var(--van-progress-pivot-padding);
    color: var(--van-progress-pivot-text-color);
    font-size: var(--van-progress-pivot-font-size);
    line-height: var(--van-progress-pivot-line-height);
    text-align: center;
    word-break: keep-all;
    background: var(--van-progress-pivot-background);
    border-radius: 1em;
    transition: all var(--van-duration-base) var(--van-ease-out);
}
.van-progress__pivot--inactive {
    background: var(--van-progress-inactive-color);
}
:host,
:root {
    --van-rolling-text-background: inherit;
    --van-rolling-text-color: var(--van-text-color);
    --van-rolling-text-font-size: var(--van-font-size-md);
    --van-rolling-text-gap: 0px;
    --van-rolling-text-item-width: 15px;
    --van-rolling-text-item-border-radius: 0px;
}
.van-rolling-text {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    font-size: var(--van-rolling-text-font-size);
    color: var(--van-rolling-text-color);
}
.van-rolling-text-item {
    margin-right: var(--van-rolling-text-gap);
    width: var(--van-rolling-text-item-width);
    border-radius: var(--van-rolling-text-item-border-radius);
    background: var(--van-rolling-text-background);
    overflow: hidden;
}
.van-rolling-text-item:last-child {
    margin-right: 0;
}
.van-rolling-text-item__box {
    overflow: hidden;
}
.van-rolling-text-item__box--animate {
    animation: van-up var(--van-duration) ease-in-out var(--van-delay);
    animation-iteration-count: 1;
    animation-fill-mode: both;
}
.van-rolling-text-item__item {
    text-align: center;
}
.van-rolling-text-item--down .van-rolling-text-item__box {
    transform: translateY(var(--van-translate));
}
.van-rolling-text-item--down .van-rolling-text-item__box--animate {
    animation-name: van-down;
}
@keyframes van-down {
    0% {
        transform: translateY(var(--van-translate));
    }
    to {
        transform: translateY(0);
    }
}
@keyframes van-up {
    0% {
        transform: translateY(0);
    }
    to {
        transform: translateY(var(--van-translate));
    }
}
:host,
:root {
    --van-sidebar-width: 80px;
}
.van-sidebar {
    width: var(--van-sidebar-width);
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}
:host,
:root {
    --van-sidebar-font-size: var(--van-font-size-md);
    --van-sidebar-line-height: var(--van-line-height-md);
    --van-sidebar-text-color: var(--van-text-color);
    --van-sidebar-disabled-text-color: var(--van-text-color-3);
    --van-sidebar-padding: 20px var(--van-padding-sm);
    --van-sidebar-active-color: var(--van-active-color);
    --van-sidebar-background: var(--van-background);
    --van-sidebar-selected-font-weight: var(--van-font-bold);
    --van-sidebar-selected-text-color: var(--van-text-color);
    --van-sidebar-selected-border-width: 4px;
    --van-sidebar-selected-border-height: 16px;
    --van-sidebar-selected-border-color: var(--van-primary-color);
    --van-sidebar-selected-background: var(--van-background-2);
}
.van-sidebar-item {
    position: relative;
    display: block;
    box-sizing: border-box;
    padding: var(--van-sidebar-padding);
    overflow: hidden;
    color: var(--van-sidebar-text-color);
    font-size: var(--van-sidebar-font-size);
    line-height: var(--van-sidebar-line-height);
    background: var(--van-sidebar-background);
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
}
.van-sidebar-item:active {
    background-color: var(--van-sidebar-active-color);
}
.van-sidebar-item:not(:last-child):after {
    border-bottom-width: 1px;
}
.van-sidebar-item__text {
    word-break: break-all;
}
.van-sidebar-item--select {
    color: var(--van-sidebar-selected-text-color);
    font-weight: var(--van-sidebar-selected-font-weight);
}
.van-sidebar-item--select,
.van-sidebar-item--select:active {
    background-color: var(--van-sidebar-selected-background);
}
.van-sidebar-item--select:before {
    position: absolute;
    top: 50%;
    left: 0;
    width: var(--van-sidebar-selected-border-width);
    height: var(--van-sidebar-selected-border-height);
    background-color: var(--van-sidebar-selected-border-color);
    transform: translateY(-50%);
    content: "";
}
.van-sidebar-item--disabled {
    color: var(--van-sidebar-disabled-text-color);
    cursor: not-allowed;
}
.van-sidebar-item--disabled:active {
    background-color: var(--van-sidebar-background);
}
:host,
:root {
    --van-tree-select-font-size: var(--van-font-size-md);
    --van-tree-select-nav-background: var(--van-background);
    --van-tree-select-content-background: var(--van-background-2);
    --van-tree-select-nav-item-padding: 14px var(--van-padding-sm);
    --van-tree-select-item-height: 48px;
    --van-tree-select-item-active-color: var(--van-primary-color);
    --van-tree-select-item-disabled-color: var(--van-gray-5);
    --van-tree-select-item-selected-size: 16px;
}
.van-tree-select {
    position: relative;
    display: flex;
    font-size: var(--van-tree-select-font-size);
}
.van-tree-select__nav {
    flex: 1;
    overflow-y: auto;
    background: var(--van-tree-select-nav-background);
    -webkit-overflow-scrolling: touch;
}
.van-tree-select__nav-item {
    padding: var(--van-tree-select-nav-item-padding);
}
.van-tree-select__content {
    flex: 2;
    overflow-y: auto;
    background: var(--van-tree-select-content-background);
    -webkit-overflow-scrolling: touch;
}
.van-tree-select__item {
    position: relative;
    padding: 0 32px 0 var(--van-padding-md);
    font-weight: var(--van-font-bold);
    line-height: var(--van-tree-select-item-height);
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    cursor: pointer;
}
.van-tree-select__item--active {
    color: var(--van-tree-select-item-active-color);
}
.van-tree-select__item:active {
    background-color: var(--van-active-color);
}
.van-tree-select__item--disabled {
    color: var(--van-tree-select-item-disabled-color);
    cursor: not-allowed;
}
.van-tree-select__item--disabled:active {
    background-color: transparent;
}
.van-tree-select__selected {
    position: absolute;
    top: 50%;
    right: var(--van-padding-md);
    margin-top: calc(var(--van-padding-xs) * -1);
    font-size: var(--van-tree-select-item-selected-size);
}
:host,
:root {
    --van-skeleton-title-width: 40%;
}
.van-skeleton-title {
    height: var(--van-skeleton-paragraph-height);
    background: var(--van-skeleton-paragraph-background);
}
.van-skeleton-title--round {
    border-radius: var(--van-radius-max);
}
.van-skeleton-title {
    width: var(--van-skeleton-title-width);
    margin: 0;
}
.van-skeleton-title + .van-skeleton-paragraph {
    margin-top: 20px;
}
:host,
:root {
    --van-skeleton-avatar-size: 32px;
    --van-skeleton-avatar-background: var(--van-active-color);
}
.van-skeleton-avatar {
    flex-shrink: 0;
    width: var(--van-skeleton-avatar-size);
    height: var(--van-skeleton-avatar-size);
    margin-right: var(--van-padding-md);
    background: var(--van-skeleton-avatar-background);
}
.van-skeleton-avatar--round {
    border-radius: var(--van-radius-max);
}
.van-skeleton-avatar + .van-skeleton__content {
    padding-top: var(--van-padding-xs);
}
:host,
:root {
    --van-skeleton-paragraph-height: 16px;
    --van-skeleton-paragraph-background: var(--van-active-color);
    --van-skeleton-paragraph-margin-top: var(--van-padding-sm);
}
.van-skeleton-paragraph {
    height: var(--van-skeleton-paragraph-height);
    background: var(--van-skeleton-paragraph-background);
}
.van-skeleton-paragraph--round {
    border-radius: var(--van-radius-max);
}
.van-skeleton-paragraph:not(:first-child) {
    margin-top: var(--van-skeleton-paragraph-margin-top);
}
:host,
:root {
    --van-skeleton-duration: 1.2s;
}
.van-skeleton {
    display: flex;
    padding: 0 var(--van-padding-md);
}
.van-skeleton__content {
    width: 100%;
}
.van-skeleton--animate {
    animation: van-skeleton-blink var(--van-skeleton-duration) ease-in-out infinite;
}
@keyframes van-skeleton-blink {
    50% {
        opacity: 0.6;
    }
}
:host,
:root {
    --van-slider-active-background: var(--van-primary-color);
    --van-slider-inactive-background: var(--van-gray-3);
    --van-slider-disabled-opacity: var(--van-disabled-opacity);
    --van-slider-bar-height: 2px;
    --van-slider-button-width: 24px;
    --van-slider-button-height: 24px;
    --van-slider-button-radius: 50%;
    --van-slider-button-background: var(--van-white);
    --van-slider-button-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}
.van-theme-dark {
    --van-slider-inactive-background: var(--van-background-3);
}
.van-slider {
    position: relative;
    width: 100%;
    height: var(--van-slider-bar-height);
    background: var(--van-slider-inactive-background);
    border-radius: var(--van-radius-max);
    cursor: pointer;
}
.van-slider:before {
    position: absolute;
    top: calc(var(--van-padding-xs) * -1);
    right: 0;
    bottom: calc(var(--van-padding-xs) * -1);
    left: 0;
    content: "";
}
.van-slider__bar {
    position: absolute;
    width: 100%;
    height: 100%;
    background: var(--van-slider-active-background);
    border-radius: inherit;
    transition: all var(--van-duration-fast);
}
.van-slider__button {
    width: var(--van-slider-button-width);
    height: var(--van-slider-button-height);
    background: var(--van-slider-button-background);
    border-radius: var(--van-slider-button-radius);
    box-shadow: var(--van-slider-button-shadow);
}
.van-slider__button-wrapper {
    position: absolute;
    cursor: grab;
    top: 50%;
}
.van-slider__button-wrapper--right {
    right: 0;
    transform: translate3d(50%, -50%, 0);
}
.van-slider__button-wrapper--left {
    left: 0;
    transform: translate3d(-50%, -50%, 0);
}
.van-slider--disabled {
    cursor: not-allowed;
    opacity: var(--van-slider-disabled-opacity);
}
.van-slider--disabled .van-slider__button-wrapper {
    cursor: not-allowed;
}
.van-slider--vertical {
    display: inline-block;
    width: var(--van-slider-bar-height);
    height: 100%;
}
.van-slider--vertical .van-slider__button-wrapper--right {
    top: auto;
    right: 50%;
    bottom: 0;
    transform: translate3d(50%, 50%, 0);
}
.van-slider--vertical .van-slider__button-wrapper--left {
    top: 0;
    right: 50%;
    left: auto;
    transform: translate3d(50%, -50%, 0);
}
.van-slider--vertical:before {
    top: 0;
    right: calc(var(--van-padding-xs) * -1);
    bottom: 0;
    left: calc(var(--van-padding-xs) * -1);
}
.van-space {
    display: inline-flex;
}
.van-space--horizontal .van-space-item {
    display: flex;
    align-items: center;
}
.van-space--vertical {
    flex-direction: column;
}
.van-space--align-baseline {
    align-items: baseline;
}
.van-space--align-start {
    align-items: flex-start;
}
.van-space--align-end {
    align-items: flex-end;
}
.van-space--align-center {
    align-items: center;
}
.van-space--wrap {
    flex-wrap: wrap;
}
.van-space--fill {
    display: flex;
}
:host,
:root {
    --van-steps-background: var(--van-background-2);
}
.van-steps {
    overflow: hidden;
    background-color: var(--van-steps-background);
}
.van-steps--horizontal {
    padding: 10px 10px 0;
}
.van-steps--horizontal .van-steps__items {
    position: relative;
    display: flex;
    margin: 0 0 10px;
    padding-bottom: 22px;
}
.van-steps--vertical {
    padding: 0 0 0 var(--van-padding-xl);
}
:host,
:root {
    --van-step-text-color: var(--van-text-color-2);
    --van-step-active-color: var(--van-primary-color);
    --van-step-process-text-color: var(--van-text-color);
    --van-step-font-size: var(--van-font-size-md);
    --van-step-line-color: var(--van-border-color);
    --van-step-finish-line-color: var(--van-primary-color);
    --van-step-finish-text-color: var(--van-text-color);
    --van-step-icon-size: 12px;
    --van-step-circle-size: 5px;
    --van-step-circle-color: var(--van-gray-6);
    --van-step-horizontal-title-font-size: var(--van-font-size-sm);
}
.van-step {
    position: relative;
    flex: 1;
    color: var(--van-step-text-color);
    font-size: var(--van-step-font-size);
}
.van-step__circle {
    display: block;
    width: var(--van-step-circle-size);
    height: var(--van-step-circle-size);
    background-color: var(--van-step-circle-color);
    border-radius: 50%;
}
.van-step__line {
    position: absolute;
    background-color: var(--van-step-line-color);
    transition: background-color var(--van-duration-base);
}
.van-step--horizontal {
    float: left;
}
.van-step--horizontal:first-child .van-step__title {
    margin-left: 0;
    transform: none;
}
.van-step--horizontal:last-child:not(:first-child) {
    position: absolute;
    right: 1px;
    width: auto;
}
.van-step--horizontal:last-child:not(:first-child) .van-step__title {
    margin-left: 0;
    transform: none;
}
.van-step--horizontal:last-child:not(:first-child) .van-step__circle-container {
    right: -9px;
    left: auto;
}
.van-step--horizontal .van-step__circle-container {
    position: absolute;
    top: 30px;
    left: calc(var(--van-padding-xs) * -1);
    z-index: 1;
    padding: 0 var(--van-padding-xs);
    background-color: var(--van-background-2);
    transform: translateY(-50%);
}
.van-step--horizontal .van-step__title {
    display: inline-block;
    margin-left: 3px;
    font-size: var(--van-step-horizontal-title-font-size);
    transform: translate(-50%);
}
.van-step--horizontal .van-step__line {
    top: 30px;
    left: 0;
    width: 100%;
    height: 1px;
}
.van-step--horizontal .van-step__icon {
    display: block;
    font-size: var(--van-step-icon-size);
}
.van-step--horizontal .van-step--process {
    color: var(--van-step-process-text-color);
}
.van-step--vertical {
    display: block;
    float: none;
    padding: 10px 10px 10px 0;
    line-height: var(--van-line-height-sm);
}
.van-step--vertical:not(:last-child):after {
    border-bottom-width: 1px;
}
.van-step--vertical .van-step__circle-container {
    position: absolute;
    top: 19px;
    left: -15px;
    z-index: 1;
    font-size: var(--van-step-icon-size);
    line-height: 1;
    transform: translate(-50%, -50%);
}
.van-step--vertical .van-step__line {
    top: 16px;
    left: -15px;
    width: 1px;
    height: 100%;
}
.van-step:last-child .van-step__line {
    width: 0;
}
.van-step--finish {
    color: var(--van-step-finish-text-color);
}
.van-step--finish .van-step__circle,
.van-step--finish .van-step__line {
    background-color: var(--van-step-finish-line-color);
}
.van-step__icon,
.van-step__title {
    transition: color var(--van-duration-base);
}
.van-step__icon--active,
.van-step__icon--finish,
.van-step__title--active,
.van-step__title--finish {
    color: var(--van-step-active-color);
}
:host,
:root {
    --van-stepper-background: var(--van-active-color);
    --van-stepper-button-icon-color: var(--van-text-color);
    --van-stepper-button-disabled-color: var(--van-background);
    --van-stepper-button-disabled-icon-color: var(--van-gray-5);
    --van-stepper-button-round-theme-color: var(--van-primary-color);
    --van-stepper-input-width: 32px;
    --van-stepper-input-height: 28px;
    --van-stepper-input-font-size: var(--van-font-size-md);
    --van-stepper-input-line-height: normal;
    --van-stepper-input-text-color: var(--van-text-color);
    --van-stepper-input-disabled-text-color: var(--van-text-color-3);
    --van-stepper-input-disabled-background: var(--van-active-color);
    --van-stepper-radius: var(--van-radius-md);
}
.van-stepper {
    display: inline-block;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
}
.van-stepper__minus,
.van-stepper__plus {
    position: relative;
    box-sizing: border-box;
    width: var(--van-stepper-input-height);
    height: var(--van-stepper-input-height);
    margin: 0;
    padding: 0;
    color: var(--van-stepper-button-icon-color);
    vertical-align: middle;
    background: var(--van-stepper-background);
    border: 0;
}
.van-stepper__minus:before,
.van-stepper__plus:before {
    width: 50%;
    height: 1px;
}
.van-stepper__minus:after,
.van-stepper__plus:after {
    width: 1px;
    height: 50%;
}
.van-stepper__minus:after,
.van-stepper__minus:before,
.van-stepper__plus:after,
.van-stepper__plus:before {
    position: absolute;
    top: 50%;
    left: 50%;
    background-color: currentColor;
    transform: translate(-50%, -50%);
    content: "";
}
.van-stepper__minus--disabled,
.van-stepper__plus--disabled {
    color: var(--van-stepper-button-disabled-icon-color);
    background-color: var(--van-stepper-button-disabled-color);
    cursor: not-allowed;
}
.van-stepper__minus {
    border-radius: var(--van-stepper-radius) 0 0 var(--van-stepper-radius);
}
.van-stepper__minus:after {
    display: none;
}
.van-stepper__plus {
    border-radius: 0 var(--van-stepper-radius) var(--van-stepper-radius) 0;
}
.van-stepper__input {
    box-sizing: border-box;
    width: var(--van-stepper-input-width);
    height: var(--van-stepper-input-height);
    margin: 0 2px;
    padding: 0;
    color: var(--van-stepper-input-text-color);
    font-size: var(--van-stepper-input-font-size);
    line-height: var(--van-stepper-input-line-height);
    text-align: center;
    vertical-align: middle;
    background: var(--van-stepper-background);
    border: 0;
    border-width: 1px 0;
    border-radius: 0;
    -webkit-appearance: none;
}
.van-stepper__input:disabled {
    color: var(--van-stepper-input-disabled-text-color);
    background-color: var(--van-stepper-input-disabled-background);
    -webkit-text-fill-color: var(--van-stepper-input-disabled-text-color);
    opacity: 1;
}
.van-stepper__input:-moz-read-only {
    cursor: default;
}
.van-stepper__input:read-only {
    cursor: default;
}
.van-stepper--round .van-stepper__input {
    background-color: transparent;
}
.van-stepper--round .van-stepper__minus,
.van-stepper--round .van-stepper__plus {
    border-radius: 100%;
}
.van-stepper--round .van-stepper__minus--disabled,
.van-stepper--round .van-stepper__plus--disabled {
    opacity: 0.3;
    cursor: not-allowed;
}
.van-stepper--round .van-stepper__plus {
    color: var(--van-white);
    background: var(--van-stepper-button-round-theme-color);
}
.van-stepper--round .van-stepper__minus {
    color: var(--van-stepper-button-round-theme-color);
    background-color: var(--van-background-2);
    border: 1px solid var(--van-stepper-button-round-theme-color);
}
.van-swipe-cell {
    position: relative;
    overflow: hidden;
    cursor: grab;
}
.van-swipe-cell__wrapper {
    transition-timing-function: cubic-bezier(0.18, 0.89, 0.32, 1);
    transition-property: transform;
}
.van-swipe-cell__left,
.van-swipe-cell__right {
    position: absolute;
    top: 0;
    height: 100%;
}
.van-swipe-cell__left {
    left: 0;
    transform: translate3d(-100%, 0, 0);
}
.van-swipe-cell__right {
    right: 0;
    transform: translate3d(100%, 0, 0);
}
:host,
:root {
    --van-tabbar-height: 50px;
    --van-tabbar-z-index: 1;
    --van-tabbar-background: var(--van-background-2);
}
.van-tabbar {
    z-index: var(--van-tabbar-z-index);
    display: flex;
    box-sizing: content-box;
    width: 100%;
    height: var(--van-tabbar-height);
    background: var(--van-tabbar-background);
}
.van-tabbar--fixed {
    position: fixed;
    bottom: 0;
    left: 0;
}
:host,
:root {
    --van-tabbar-item-font-size: var(--van-font-size-sm);
    --van-tabbar-item-text-color: var(--van-text-color);
    --van-tabbar-item-active-color: var(--van-primary-color);
    --van-tabbar-item-active-background: var(--van-background-2);
    --van-tabbar-item-line-height: 1;
    --van-tabbar-item-icon-size: 22px;
    --van-tabbar-item-icon-margin-bottom: var(--van-padding-base);
}
.van-tabbar-item {
    display: flex;
    flex: 1;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--van-tabbar-item-text-color);
    font-size: var(--van-tabbar-item-font-size);
    line-height: var(--van-tabbar-item-line-height);
    cursor: pointer;
}
.van-tabbar-item__icon {
    margin-bottom: var(--van-tabbar-item-icon-margin-bottom);
    font-size: var(--van-tabbar-item-icon-size);
}
.van-tabbar-item__icon .van-icon {
    display: block;
}
.van-tabbar-item__icon .van-badge {
    margin-top: var(--van-padding-base);
}
.van-tabbar-item__icon img {
    display: block;
    height: 20px;
}
.van-tabbar-item--active {
    color: var(--van-tabbar-item-active-color);
    background-color: var(--van-tabbar-item-active-background);
}
:host,
:root {
    --van-text-ellipsis-line-height: 1.6;
    --van-text-ellipsis-action-color: var(--van-blue);
}
.van-text-ellipsis {
    line-height: var(--van-text-ellipsis-line-height);
    white-space: pre-wrap;
    overflow-wrap: break-word;
}
.van-text-ellipsis__action {
    cursor: pointer;
    color: var(--van-text-ellipsis-action-color);
}
.van-text-ellipsis__action:active {
    opacity: var(--van-active-opacity);
}
:host,
:root {
    --van-watermark-z-index: 100;
}
.van-watermark {
    position: absolute;
    height: 100%;
    width: 100%;
    left: 0;
    top: 0;
    z-index: var(--van-watermark-z-index);
    background-repeat: repeat;
    pointer-events: none;
}
.van-watermark__wrapper {
    display: none;
}
.van-watermark--full {
    position: fixed;
}

/* ===== 复选框样式 ===== */
/* 未选中状态的复选框图标透明度设置 */
.van-checkbox--unchecked .van-icon[data-v-e714f03a] {
    opacity: 0;
}

/* 自定义复选框样式 */
.custom-checkbox .van-checkbox__icon--unchecked .van-icon[data-v-e714f03a] {
    visibility: hidden; /* 未选中时隐藏图标 */
}

.custom-checkbox .van-checkbox__icon--checked .van-icon[data-v-e714f03a] {
    visibility: visible; /* 选中时显示图标 */
}

/* ===== 表单组件布局 ===== */
/* 复选框组：2列网格布局 */
.checkbox-group[data-v-e714f03a] {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr)); /* 2列等宽布局 */
    gap: 10px 0; /* 行间距10px，列间距0 */
}

/* 单选框组：4列网格布局 */
.radio-group[data-v-e714f03a] {
    display: grid;
    grid-template-columns: repeat(4, minmax(0, 1fr)); /* 4列等宽布局 */
    gap: 10px 0; /* 行间距10px，列间距0 */
}

/* 捐献选项组：5列网格布局 */
.juanxian-group[data-v-e714f03a] {
    display: grid;
    grid-template-columns: repeat(5, minmax(0, 1fr)); /* 5列等宽布局 */
    gap: 10px 0; /* 行间距10px，列间距0 */
}

/* 复选框项目样式 */
.checkbox-item[data-v-e714f03a] {
    flex: 1 1 calc(50% - 10px); /* 弹性布局，占50%宽度减去间距 */
    box-sizing: border-box; /* 包含边框和内边距在内的盒模型 */
}

/* ===== 页面布局 ===== */
/* 主内容区域 */
.content[data-v-e714f03a] {
    min-height: 100vh; /* 最小高度为视窗高度 */
    display: flex;
    flex-direction: column; /* 垂直排列 */
    margin-top: 146px; /* 顶部留出导航栏空间 */
    line-height: 1.1; /* 行高设置 */
}

/* 居中对齐工具类 */
.keepCenter[data-v-e714f03a] {
    margin-left: auto;
    margin-right: auto;
}

/* ===== 导航和标签页 ===== */
/* 固定导航栏 */
.fixed-nav-bar[data-v-e714f03a] {
    position: fixed; /* 固定定位 */
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000; /* 高层级，确保在最上层 */
}

/* 粘性标签页 */
.sticky-tabs[data-v-e714f03a] {
    position: sticky; /* 粘性定位 */
    z-index: 999; /* 略低于导航栏的层级 */
}

/* ===== 表单样式 ===== */
/* 表单内容区域 */
.form-content[data-v-e714f03a] {
    margin-top: 92px; /* 顶部间距 */
    padding: 16px; /* 内边距 */
}

/* 步进器容器 */
.stepper-container[data-v-e714f03a] {
    margin-left: auto; /* 右对齐 */
}

/* 自适应文本 */
.adaptive-text[data-v-e714f03a] {
    margin-right: 20px; /* 右边距 */
}

/* 单选框组容器 */
.radio-group-container[data-v-e714f03a] {
    display: flex;
    flex-direction: column; /* 垂直排列 */
    align-items: flex-start; /* 左对齐 */
    gap: 10px; /* 项目间距 */
}

/* 单选框项目 */
.radio-item[data-v-e714f03a] {
    width: 100%; /* 全宽 */
    text-align: center; /* 文本居中 */
}

/* ===== 主题样式 ===== */
/* 深色主题下的body样式 */
.van-theme-dark body {
    color: #000; /* 文字颜色 */
    background-color: #000; /* 背景颜色 */
}

/* ===== CSS变量定义 ===== */
:root {
    --van-animation-duration-fast: 0.15s; /* 快速动画持续时间 */
}

/* ===== 弹窗动画 ===== */
.van-popup {
    transition-duration: var(--van-animation-duration-fast) !important;
}

/* ===== 其他组件样式 ===== */
/* 未选中复选框的通用样式 */
.van-checkbox--unchecked .van-icon {
    opacity: 0;
}

/* 图标样式 */
.img-icon {
    height: 20px; /* 图标高度 */
}
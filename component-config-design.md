# WebUI组件抽象化架构设计

## 概述
设计一个通用的组件配置系统，使Lua后端可以通过JSON配置动态生成前端界面，无需修改前端代码。

## 核心设计理念
- **数据驱动**: 前端界面完全由Lua传递的配置数据决定
- **组件化**: 将所有UI元素抽象为可配置的组件
- **类型安全**: 严格的组件类型定义和属性验证
- **扩展性**: 易于添加新的组件类型

## 组件类型定义

### 1. 基础组件类型
```json
{
  "componentTypes": {
    "switch": "开关组件",
    "stepper": "步进器组件", 
    "checkbox-group": "复选框组",
    "radio-group": "单选框组",
    "input": "输入框组件",
    "picker": "选择器组件",
    "tabs": "标签页组件",
    "button": "按钮组件",
    "text": "文本显示组件"
  }
}
```

### 2. 通用组件属性
```json
{
  "baseProperties": {
    "id": "组件唯一标识符",
    "type": "组件类型",
    "name": "组件名称(用于数据绑定)",
    "label": "显示标签",
    "description": "组件描述/说明",
    "visible": "是否可见",
    "disabled": "是否禁用",
    "required": "是否必填",
    "defaultValue": "默认值",
    "validation": "验证规则",
    "style": "自定义样式",
    "className": "CSS类名"
  }
}
```

## 具体组件配置规范

### Switch 开关组件
```json
{
  "type": "switch",
  "id": "smart_strategy",
  "name": "智能策略",
  "label": "智能策略:",
  "description": "全功能随主城等级自动调整",
  "defaultValue": false,
  "properties": {
    "size": "normal|large|small",
    "activeColor": "#1989fa",
    "inactiveColor": "#dcdee0"
  }
}
```

### Stepper 步进器组件
```json
{
  "type": "stepper",
  "id": "script_interval",
  "name": "脚本间隔",
  "label": "脚本间隔(秒):",
  "defaultValue": 5,
  "properties": {
    "min": 0,
    "max": 60,
    "step": 5,
    "disableInput": true,
    "theme": "round",
    "showText": {
      "0": "不间隔",
      "60": "最大间隔"
    }
  }
}
```

### Checkbox Group 复选框组
```json
{
  "type": "checkbox-group",
  "id": "research_projects",
  "name": "科研项目",
  "label": "科研项目:",
  "defaultValue": [],
  "properties": {
    "direction": "horizontal|vertical",
    "max": 0,
    "shape": "square|round",
    "options": [
      {"value": "基础战斗", "label": "基础战斗"},
      {"value": "高级战斗", "label": "高级战斗"},
      {"value": "防御研究", "label": "防御研究"}
    ],
    "layout": {
      "columns": 2,
      "gap": "10px"
    }
  }
}
```

### Radio Group 单选框组
```json
{
  "type": "radio-group",
  "id": "rally_method",
  "name": "集结方式",
  "label": "集结组队方式:",
  "defaultValue": "不集结",
  "properties": {
    "direction": "horizontal|vertical",
    "options": [
      {"value": "不集结", "label": "不集结"},
      {"value": "混队", "label": "混队"},
      {"value": "建队", "label": "建队"},
      {"value": "先混再建", "label": "先混再建"}
    ],
    "layout": {
      "columns": 4,
      "gap": "10px"
    }
  }
}
```

### Picker 选择器组件
```json
{
  "type": "picker",
  "id": "building_select",
  "name": "选择建筑",
  "label": "优先学习:",
  "defaultValue": "",
  "properties": {
    "columns": [
      {
        "values": [
          {"text": "协同作战1", "value": "协同作战1"},
          {"text": "协同作战2", "value": "协同作战2"},
          {"text": "协同作战3", "value": "协同作战3"}
        ]
      }
    ],
    "showToolbar": true,
    "title": "选择建筑",
    "confirmButtonText": "确认",
    "cancelButtonText": "取消"
  }
}
```

## 页面布局配置

### Tabs 标签页配置
```json
{
  "type": "tabs",
  "id": "main_tabs",
  "properties": {
    "sticky": true,
    "swipeable": true,
    "animated": true,
    "tabs": [
      {
        "name": "basic",
        "title": "基础设置",
        "components": ["smart_strategy", "script_interval"]
      },
      {
        "name": "research", 
        "title": "科研设置",
        "components": ["research_projects", "research_interval"]
      }
    ]
  }
}
```

## 数据结构规范

### 完整页面配置
```json
{
  "pageConfig": {
    "title": "游戏配置界面",
    "theme": "dark",
    "layout": {
      "type": "tabs",
      "config": "main_tabs"
    },
    "components": {
      "smart_strategy": { /* Switch配置 */ },
      "script_interval": { /* Stepper配置 */ },
      "research_projects": { /* Checkbox Group配置 */ }
    },
    "validation": {
      "rules": [],
      "messages": {}
    },
    "actions": {
      "submit": "/api/submit-form",
      "exit": "/api/exit"
    }
  }
}
```

## Lua配置接口设计

### 配置生成函数
```lua
function generatePageConfig()
  local config = {
    pageConfig = {
      title = "我的游戏配置",
      theme = "dark",
      components = {},
      layout = {
        type = "tabs",
        config = {
          tabs = {}
        }
      }
    }
  }
  
  -- 添加组件
  config.pageConfig.components.smart_strategy = createSwitchComponent({
    name = "智能策略",
    label = "智能策略:",
    description = "全功能随主城等级自动调整",
    defaultValue = false
  })
  
  return json.encode(config)
end
```

## 实现计划
1. 创建Vue.js动态组件渲染器
2. 实现组件工厂模式
3. 添加配置验证系统
4. 扩展Lua API接口
5. 创建配置模板库

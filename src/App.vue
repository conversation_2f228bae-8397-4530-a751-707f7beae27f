<template>
  <div id="app">
    <van-config-provider :theme="pageConfig.theme || 'dark'">
      <div class="form-container">
        <!-- 导航栏 -->
        <van-nav-bar
          :title="pageConfig.title || '配置界面'"
          left-text="退出"
          right-text="确定"
          @click-left="onExit"
          @click-right="onSubmit"
          class="fixed-nav-bar"
        />
        
        <!-- 主表单 -->
        <van-form ref="formRef" @submit="onSubmit">
          <!-- 动态渲染布局 -->
          <component
            :is="layoutComponent"
            :config="pageConfig.layout"
            :components="pageConfig.components"
            :formData="formData"
            @update="updateFormData"
          />
        </van-form>
      </div>
    </van-config-provider>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import DynamicTabs from './components/DynamicTabs.vue'
import DynamicForm from './components/DynamicForm.vue'

export default {
  name: 'App',
  components: {
    DynamicTabs,
    DynamicForm
  },
  setup() {
    // 页面配置数据
    const pageConfig = ref({
      title: '配置界面',
      theme: 'dark',
      layout: {
        type: 'form'
      },
      components: {},
      actions: {
        submit: '/api/submit-form',
        exit: '/api/exit'
      }
    })
    
    // 表单数据
    const formData = reactive({})
    
    // 表单引用
    const formRef = ref(null)
    
    // 计算布局组件
    const layoutComponent = computed(() => {
      const layoutType = pageConfig.value.layout?.type || 'form'
      switch (layoutType) {
        case 'tabs':
          return 'DynamicTabs'
        case 'form':
        default:
          return 'DynamicForm'
      }
    })
    
    // 更新表单数据
    const updateFormData = (name, value) => {
      formData[name] = value
    }
    
    // 初始化表单数据
    const initFormData = () => {
      const components = pageConfig.value.components || {}
      Object.keys(components).forEach(key => {
        const component = components[key]
        if (component.defaultValue !== undefined) {
          formData[key] = component.defaultValue
        }
      })
    }
    
    // 提交表单
    const onSubmit = async () => {
      try {
        const submitUrl = pageConfig.value.actions?.submit || '/api/submit-form'
        const response = await fetch(submitUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(formData)
        })
        
        if (response.ok) {
          console.log('配置提交成功')
        }
      } catch (error) {
        console.error('提交失败:', error)
      }
    }
    
    // 退出应用
    const onExit = async () => {
      try {
        const exitUrl = pageConfig.value.actions?.exit || '/api/exit'
        await fetch(exitUrl)
      } catch (error) {
        console.error('退出失败:', error)
      }
    }
    
    // 加载配置
    const loadConfig = async () => {
      try {
        const response = await fetch('/api/config')
        if (response.ok) {
          const config = await response.json()
          pageConfig.value = config.pageConfig || pageConfig.value
          initFormData()
        }
      } catch (error) {
        console.error('加载配置失败:', error)
        // 使用默认配置
        initFormData()
      }
    }
    
    // 组件挂载时加载配置
    onMounted(() => {
      loadConfig()
    })
    
    return {
      pageConfig,
      formData,
      formRef,
      layoutComponent,
      updateFormData,
      onSubmit,
      onExit
    }
  }
}
</script>

<style>
/* 基础样式 */
#app {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.form-container {
  min-height: 100vh;
  padding-top: 46px; /* 为固定导航栏留出空间 */
}

.fixed-nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}
</style>

<template>
  <van-field 
    :name="componentId"
    :label="config.label"
    :required="config.required"
  >
    <template #input>
      <div class="switch-container">
        <label 
          v-if="config.description"
          :for="`${componentId}-switch`"
          class="switch-description"
        >
          {{ config.description }}
        </label>
        <van-switch
          :id="`${componentId}-switch`"
          :model-value="modelValue"
          @update:model-value="$emit('update:modelValue', $event)"
          :size="config.properties?.size || 'normal'"
          :active-color="config.properties?.activeColor"
          :inactive-color="config.properties?.inactiveColor"
          :disabled="config.disabled"
        />
      </div>
    </template>
  </van-field>
</template>

<script>
export default {
  name: 'DynamicSwitch',
  props: {
    config: {
      type: Object,
      required: true
    },
    componentId: {
      type: String,
      required: true
    },
    modelValue: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:modelValue']
}
</script>

<style scoped>
.switch-container {
  display: flex;
  align-items: center;
  width: 100%;
}

.switch-description {
  margin-right: 10px;
  flex: 1;
  font-size: 14px;
  color: var(--van-text-color-2);
}
</style>

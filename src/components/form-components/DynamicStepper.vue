<template>
  <van-field 
    :name="componentId"
    :label="config.label"
    :required="config.required"
  >
    <template #input>
      <div class="stepper-container">
        <!-- 显示特殊文本 -->
        <span 
          v-if="showText"
          class="stepper-text"
        >
          {{ showText }}
        </span>
        
        <!-- 步进器 -->
        <van-stepper
          :model-value="modelValue"
          @update:model-value="$emit('update:modelValue', $event)"
          :min="config.properties?.min || 0"
          :max="config.properties?.max || 100"
          :step="config.properties?.step || 1"
          :theme="config.properties?.theme || 'round'"
          :disable-input="config.properties?.disableInput !== false"
          :disabled="config.disabled"
          class="stepper-component"
        />
      </div>
    </template>
  </van-field>
</template>

<script>
import { computed } from 'vue'

export default {
  name: 'DynamicStepper',
  props: {
    config: {
      type: Object,
      required: true
    },
    componentId: {
      type: String,
      required: true
    },
    modelValue: {
      type: Number,
      default: 0
    }
  },
  emits: ['update:modelValue'],
  setup(props) {
    // 计算显示文本
    const showText = computed(() => {
      const showTextConfig = props.config.properties?.showText
      if (showTextConfig && typeof showTextConfig === 'object') {
        return showTextConfig[props.modelValue] || null
      }
      return null
    })
    
    return {
      showText
    }
  }
}
</script>

<style scoped>
.stepper-container {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 100%;
}

.stepper-text {
  margin-right: 20px;
  font-size: 14px;
  color: var(--van-text-color-2);
}

.stepper-component {
  margin-left: auto;
}
</style>

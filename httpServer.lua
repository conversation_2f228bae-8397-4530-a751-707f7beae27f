-- HTTP服务器模块
require("TSLib")

-- 日志记录函数
function log(message)
  local timestamp = os.date()
  local logMessage = timestamp .. (message or "null")
  nLog(logMessage)
end

-- 初始化
log("start", "RT")
keep = true
tsp = checkScriptAuth()

-- 加载socket库
local socket = require("socket")
socket.tcp4 = socket.tcp4 or socket.tcp

-- HTTP服务器对象
local httpServer = {}

-- 获取随机端口号
function httpServer.getRandomPort()
  return math.random(1024, 65535)
end

-- 检查端口是否可用
function httpServer.isPortAvailable(port)
  local testSocket = socket.tcp()
  local success, err = testSocket:bind("*", port)
  testSocket:close()

  if err then
    log("isPortAvailable" .. err)
  end

  return success ~= nil
end

-- 获取可用端口
function httpServer.getAvailablePort()
  log("开始获取port")

  -- 预定义端口列表为空，直接使用随机端口
  local preferredPorts = {}

  -- 检查预定义端口
  for _, port in ipairs(preferredPorts) do
    log("当前port" .. port)
    if httpServer.isPortAvailable(port) then
      log("获取port" .. port)
      return port
    else
      log(port .. "port占用")
    end
  end

  -- 如果预定义端口都被占用，使用随机端口
  log("基础port占用")
  local port
  repeat
    port = httpServer.getRandomPort()
    log("获取port中" .. port)
  until httpServer.isPortAvailable(port)

  log("获取port" .. port)
  return port
end

-- 启动HTTP服务器
function httpServer.start()
  local port = 8888
  
  local server = socket.tcp()
  -- 设置地址重用选项（可选，但推荐，便于重启后立即绑定）
  server:setoption('reuseaddr', true)
  -- 先绑定
  local bind_ok, bind_err = server:bind("*", port)

  if not bind_ok then
    nLog("绑定端口 " .. port .. " 失败: " .. bind_err)
    -- 尝试获取可用端口
    port = httpServer.getAvailablePort()
    bind_ok, bind_err = server:bind("*", port)
    if not bind_ok then
      nLog("再次绑定端口 " .. port .. " 也失败: " .. bind_err)
      server:close()
      return -- 或者采取其他错误处理措施
    end
  end
  
  -- 绑定成功后，开始监听。backlog 参数指定连接队列的最大长度
  local listen_ok, listen_err = server:listen(32) -- 32 是常见的 backlog 值
  if not listen_ok then
    nLog("监听失败: " .. listen_err)
    server:close()
    return
  end

  server:settimeout(0)
  ServerPort = port
  toast(port)
  log("Server listening on port " .. port)

  local serverIP, serverPort = server:getsockname()
  log("Server address: " .. serverIP .. ":" .. serverPort)

  local clients = {}

  -- 主服务循环
  while keep do
    mSleep(1)

    -- 接受新连接
    local client = server:accept()
    if client then
      client:settimeout(0)
      table.insert(clients, client)

      local clientIP, clientPort = client:getpeername()
      log("Client connected from IP: " .. clientIP .. " Port: " .. clientPort)
    end

    -- 处理现有客户端连接
    for i = #clients, 1, -1 do
      local client = clients[i]
      local success, err = pcall(httpServer.handleClient, client)

      if not success then
        log("Error handling client: " .. err)
        client:close()
        table.remove(clients, i)
      end
    end
  end

  -- 清理所有客户端连接
  for _, client in ipairs(clients) do
    client:close()
  end
  server:close()
end

-- 处理客户端请求
function httpServer.handleClient(client)
  local requestLine, err = client:receive("*l")

  if not requestLine then
    if err ~= "timeout" then
      log("Error receiving request: " .. err)
      error(err)
    end
    return
  end

  log(requestLine)

  local method, path = requestLine:match("(%S+)%s+(%S+)")
  if not method or not path then
    log("Error parsing request line")
    return
  end

  local headers = httpServer.parseHeaders(client)
  local body = nil

  if method == "POST" then
    body = httpServer.parseBody(client, headers)
  end

  -- 路由处理
  if method == "GET" then
    httpServer.handleGET(client, path)
  elseif method == "POST" then
    httpServer.handlePOST(client, path, body)
  else
    httpServer.sendResponse(client, 400, "Bad Request")
  end

  -- 如果不是keep-alive连接，关闭客户端
  if headers.Connection ~= "keep-alive" then
    client:close()
  end
end

-- 解析HTTP头部
function httpServer.parseHeaders(client)
  local headers = {}

  while true do
    local line, err = client:receive("*l")
    if not line or line == "" then
      break
    end

    local key, value = line:match("([^:]+):%s*(.*)")
    if key and value then
      headers[key] = value
    end
  end

  return headers
end

-- 解析请求体
function httpServer.parseBody(client, headers)
  local body = nil
  local contentLength = tonumber(headers["Content-Length"])

  if contentLength then
    local data, err = client:receive(contentLength)
    if err then
      log("Error receiving body: " .. err)
      return nil
    end
    body = data
  end

  return body
end

-- 服务静态文件
local function serveFile(client, filePath)
  local file, err = io.open(filePath, "r")

  if not file then
    log("文件不存在: " .. filePath)
    httpServer.sendResponse(client, 404, "Not Found")
    return
  end

  local content = file:read("*a")
  file:close()

  local contentType = httpServer.getContentType(filePath)
  httpServer.sendResponse(client, 200, "OK", content, contentType)
end

-- 将URL路径转换为文件系统路径
local function urlToFilePath(urlPath)
  local basePath = userPath() .. "/res/"

  if urlPath == "/" then
    return basePath .. "index.html"
  else
    return basePath .. urlPath:sub(2)
  end
end

-- API路由表
local apiRoutes = {}

-- 退出API
local function apiExit(client)
  httpServer.sendResponse(client, 200, "OK")
  lua_exit()
end
apiRoutes["/api/exit"] = apiExit

-- 复制到剪切板API
local function apiCopy(client)
  nLog("写入剪切板")
  writePasteboard("11975534")
  httpServer.sendResponse(client, 200, "OK")
end
apiRoutes["/api/copy"] = apiCopy

-- 停止服务器API
local function apiBreak(client)
  nLog("break")
  keep = false
  httpServer.sendResponse(client, 200, "OK")
end
apiRoutes["/api/break"] = apiBreak

-- 处理GET请求
function httpServer.handleGET(client, path)
  local filePath = urlToFilePath(path)

  -- 检查是否是API路由
  if apiRoutes[path] then
    apiRoutes[path](client)
  else
    -- 处理路径分隔符（Windows兼容性）
    if tsp.type ~= "lua" then
      filePath = filePath:gsub("static/", "static\\")
    end
    serveFile(client, filePath)
  end
end

-- 处理POST请求
function httpServer.handlePOST(client, path, body)
  if path == "/api/submit-form" and body and #body ~= 0 then
    _ENV["配置"] = body
    nLog("body" .. body)
    httpServer.sendResponse(client, 200, "OK", "Form submitted successfully")
    keep = false
  else
    httpServer.sendResponse(client, 404, "Not Found")
  end
end

-- 根据文件扩展名获取内容类型
function httpServer.getContentType(filePath)
  local extension = filePath:match("%.(%w+)$")

  local mimeTypes = {
    json = "application/json",
    html = "text/html",
    htm = "text/html",
    css = "text/css",
    js = "application/javascript",
    png = "image/png",
    jpg = "image/jpeg",
    jpeg = "image/jpeg",
    gif = "image/gif",
    bmp = "image/bmp",
    ico = "image/x-icon",
    svg = "image/svg+xml",
    xml = "application/xml",
    txt = "text/plain",
    csv = "text/csv",
    pdf = "application/pdf",
    doc = "application/msword",
    docx = "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    xls = "application/vnd.ms-excel",
    xlsx = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    ppt = "application/vnd.ms-powerpoint",
    pptx = "application/vnd.openxmlformats-officedocument.presentationml.presentation",
    zip = "application/zip",
    gz = "application/gzip",
    tar = "application/x-tar",
    ["7z"] = "application/x-7z-compressed",
    mp3 = "audio/mpeg",
    wav = "audio/wav",
    avi = "video/x-msvideo",
    mp4 = "video/mp4",
    m4v = "video/x-m4v",
    mov = "video/quicktime",
    wmv = "video/x-ms-wmv",
    flv = "video/x-flv"
  }

  return mimeTypes[extension] or "text/plain"
end

-- 发送HTTP响应
function httpServer.sendResponse(client, statusCode, statusText, content, contentType)
  contentType = contentType or "text/html"
  local contentLength = (content and #content) or 0

  local response = string.format(
    "HTTP/1.1 %d %s\r\n" ..
    "Content-Type: %s\r\n" ..
    "Content-Length: %d\r\n" ..
    "Access-Control-Allow-Origin: *\r\n" ..
    "\r\n" ..
    "%s",
    statusCode, statusText, contentType, contentLength, content or ""
  )

  client:send(response)
end

return httpServer

<template>
  <van-field
    :name="componentId"
    :label="config.label"
    :placeholder="config.properties?.placeholder || `请输入${config.label}`"
    :model-value="modelValue"
    @update:model-value="$emit('update:modelValue', $event)"
    :type="config.properties?.type || 'text'"
    :maxlength="config.properties?.maxlength"
    :readonly="config.properties?.readonly"
    :disabled="config.disabled"
    :required="config.required"
    :clearable="config.properties?.clearable !== false"
    :show-word-limit="config.properties?.showWordLimit"
    :error-message="errorMessage"
  />
</template>

<script>
import { computed } from 'vue'

export default {
  name: 'DynamicInput',
  props: {
    config: {
      type: Object,
      required: true
    },
    componentId: {
      type: String,
      required: true
    },
    modelValue: {
      type: [String, Number],
      default: ''
    }
  },
  emits: ['update:modelValue'],
  setup(props) {
    // 计算错误信息
    const errorMessage = computed(() => {
      if (!props.config.validation) return ''
      
      const validation = props.config.validation
      const value = props.modelValue
      
      // 必填验证
      if (validation.required && (!value || value.toString().trim() === '')) {
        return validation.messages?.required || `${props.config.label}不能为空`
      }
      
      // 最小长度验证
      if (validation.minLength && value && value.toString().length < validation.minLength) {
        return validation.messages?.minLength || `${props.config.label}至少需要${validation.minLength}个字符`
      }
      
      // 最大长度验证
      if (validation.maxLength && value && value.toString().length > validation.maxLength) {
        return validation.messages?.maxLength || `${props.config.label}不能超过${validation.maxLength}个字符`
      }
      
      // 正则验证
      if (validation.pattern && value) {
        const regex = new RegExp(validation.pattern)
        if (!regex.test(value.toString())) {
          return validation.messages?.pattern || `${props.config.label}格式不正确`
        }
      }
      
      return ''
    })
    
    return {
      errorMessage
    }
  }
}
</script>

<style scoped>
/* 输入框样式可以在这里自定义 */
</style>

<template>
  <div class="dynamic-text" :class="getTextClass()">
    <div v-if="config.label" class="text-label">
      {{ config.label }}
    </div>
    <div class="text-content" v-html="formattedContent"></div>
  </div>
</template>

<script>
import { computed } from 'vue'

export default {
  name: 'DynamicText',
  props: {
    config: {
      type: Object,
      required: true
    },
    componentId: {
      type: String,
      required: true
    },
    modelValue: {
      type: [String, Number],
      default: ''
    }
  },
  setup(props) {
    // 格式化内容
    const formattedContent = computed(() => {
      let content = props.config.content || props.modelValue || ''
      
      // 支持HTML内容
      if (props.config.properties?.html) {
        return content
      }
      
      // 纯文本，转义HTML
      return content.toString().replace(/</g, '&lt;').replace(/>/g, '&gt;')
    })
    
    // 获取文本样式类
    const getTextClass = () => {
      const classes = ['dynamic-text']
      
      // 文本大小
      if (props.config.properties?.size) {
        classes.push(`text-${props.config.properties.size}`)
      }
      
      // 文本颜色
      if (props.config.properties?.color) {
        classes.push(`text-${props.config.properties.color}`)
      }
      
      // 文本对齐
      if (props.config.properties?.align) {
        classes.push(`text-${props.config.properties.align}`)
      }
      
      // 自定义类名
      if (props.config.className) {
        classes.push(props.config.className)
      }
      
      return classes.join(' ')
    }
    
    return {
      formattedContent,
      getTextClass
    }
  }
}
</script>

<style scoped>
.dynamic-text {
  padding: 12px 16px;
}

.text-label {
  font-weight: 500;
  margin-bottom: 8px;
  color: var(--van-text-color);
}

.text-content {
  color: var(--van-text-color-2);
  line-height: 1.5;
}

/* 文本大小 */
.text-small .text-content {
  font-size: 12px;
}

.text-normal .text-content {
  font-size: 14px;
}

.text-large .text-content {
  font-size: 16px;
}

/* 文本颜色 */
.text-primary .text-content {
  color: var(--van-primary-color);
}

.text-success .text-content {
  color: var(--van-success-color);
}

.text-warning .text-content {
  color: var(--van-warning-color);
}

.text-danger .text-content {
  color: var(--van-danger-color);
}

/* 文本对齐 */
.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}
</style>

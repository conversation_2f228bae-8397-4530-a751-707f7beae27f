<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <!-- 字符编码设置为UTF-8，支持中文显示 -->
    <meta charset="utf-8">

    <!-- 兼容IE浏览器，使用最新的渲染引擎 -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge">

    <!-- 响应式设计，适配移动设备 -->
    <meta name="viewport" content="width=device-width,initial-scale=1">

    <!-- 网站图标 -->
    <link rel="icon" href="/favicon.ico">

    <!-- 网页标题 -->
    <title>动态配置界面</title>

    <!-- Vue 3 CDN -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>

    <!-- Vant 4 CDN -->
    <script src="https://fastly.jsdelivr.net/npm/vant@4/lib/vant.min.js"></script>
    <link rel="stylesheet" href="https://fastly.jsdelivr.net/npm/vant@4/lib/index.css" />

    <!-- 自定义样式 -->
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: #f7f8fa;
        }

        .form-container {
            min-height: 100vh;
            padding-top: 46px;
        }

        .fixed-nav-bar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
        }

        .sticky-tabs {
            position: sticky;
            top: 46px;
            z-index: 999;
        }

        .tab-content {
            padding: 16px;
            min-height: calc(100vh - 138px);
        }

        .switch-container {
            display: flex;
            align-items: center;
            width: 100%;
        }

        .switch-description {
            margin-right: 10px;
            flex: 1;
            font-size: 14px;
            color: #646566;
        }

        .stepper-container {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            width: 100%;
        }

        .stepper-text {
            margin-right: 20px;
            font-size: 14px;
            color: #646566;
        }

        .stepper-component {
            margin-left: auto;
        }

        /* 复选框组样式 */
        .checkbox-group {
            display: grid;
            grid-template-columns: repeat(2, minmax(0, 1fr));
            gap: 10px 0;
        }

        .checkbox-group-2-cols {
            display: grid;
            grid-template-columns: repeat(2, minmax(0, 1fr));
            gap: 10px 0;
        }

        .checkbox-group-3-cols {
            display: grid;
            grid-template-columns: repeat(3, minmax(0, 1fr));
            gap: 10px 0;
        }

        .checkbox-group-4-cols {
            display: grid;
            grid-template-columns: repeat(4, minmax(0, 1fr));
            gap: 10px 0;
        }

        .checkbox-group-5-cols {
            display: grid;
            grid-template-columns: repeat(5, minmax(0, 1fr));
            gap: 10px 0;
        }

        /* 单选框组样式 */
        .radio-group {
            display: grid;
            grid-template-columns: repeat(4, minmax(0, 1fr));
            gap: 10px 0;
        }

        .radio-group-2-cols {
            display: grid;
            grid-template-columns: repeat(2, minmax(0, 1fr));
            gap: 10px 0;
        }

        .radio-group-3-cols {
            display: grid;
            grid-template-columns: repeat(3, minmax(0, 1fr));
            gap: 10px 0;
        }

        .radio-group-4-cols {
            display: grid;
            grid-template-columns: repeat(4, minmax(0, 1fr));
            gap: 10px 0;
        }

        .radio-group-5-cols {
            display: grid;
            grid-template-columns: repeat(5, minmax(0, 1fr));
            gap: 10px 0;
        }

        hr {
            border: none;
            border-top: 1px solid #ebedf0;
            margin: 16px 0;
        }
    </style>
</head>
<body>
    <!-- 当JavaScript被禁用时显示的提示信息 -->
    <noscript>
        <strong>
            抱歉，此应用需要启用JavaScript才能正常工作。请启用JavaScript后继续。
        </strong>
    </noscript>

    <!-- Vue.js应用的挂载点 -->
    <div id="app"></div>

    <!-- 应用脚本 -->
    <script src="/js/dynamic-app.js"></script>
</body>
</html>
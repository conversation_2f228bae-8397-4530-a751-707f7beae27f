(function () {
    "use strict";
    var e = {
            6862: function (e, a, l) {
                var t = l(3751),
                    n = l(641);
                const u = {
                        class: "form-container"
                    },
                    o = {
                        style: {
                            display: "flex",
                            "align-items": "center"
                        }
                    },
                    i = {
                        key: 0
                    },
                    d = {
                        class: "stepper-container"
                    },
                    s = {
                        key: 0,
                        class: "adaptive-text"
                    },
                    r = {
                        key: 1,
                        class: "adaptive-text"
                    },
                    m = {
                        class: "stepper-container"
                    },
                    c = {
                        key: 0,
                        class: "adaptive-text"
                    },
                    b = {
                        key: 1,
                        class: "adaptive-text"
                    },
                    p = {
                        class: "stepper-container"
                    },
                    k = {
                        key: 0,
                        class: "adaptive-text"
                    },
                    f = {
                        key: 1,
                        class: "adaptive-text"
                    },
                    v = {
                        style: {
                            margin: "16px"
                        }
                    };

                function h(e, a, l, h, x, F) {
                    const _ = (0, n.g2)("van-nav-bar"),
                        V = (0, n.g2)("van-switch"),
                        g = (0, n.g2)("van-field"),
                        W = (0, n.g2)("van-stepper"),
                        y = (0, n.g2)("van-cell"),
                        w = (0, n.g2)("van-picker"),
                        C = (0, n.g2)("van-popup"),
                        U = (0, n.g2)("van-tab"),
                        I = (0, n.g2)("van-checkbox"),
                        O = (0, n.g2)("van-checkbox-group"),
                        q = (0, n.g2)("van-radio"),
                        L = (0, n.g2)("van-radio-group"),
                        E = (0, n.g2)("van-lebel"),
                        P = (0, n.g2)("van-tabs"),
                        Q = (0, n.g2)("van-button"),
                        X = (0, n.g2)("van-form"),
                        j = (0, n.g2)("van-config-provider");
                    return (0, n.uX)(), (0, n.Wv)(j, {
                        theme: "dark"
                    }, {
                        default: (0, n.k6)((() => [(0, n.Lk)("div", u, [(0, n.bF)(_, {
                            title: "设置",
                            "left-text": "退出",
                            "right-text": "确定",
                            onClickRight: h.onSubmit,
                            onClickLeft: h.onExit,
                            class: "fixed-nav-bar"
                        }, null, 8, ["onClickRight", "onClickLeft"]), (0, n.bF)(X, {
                            ref: "formRef",
                            onSubmit: h.onSubmit
                        }, {
                            default: (0, n.k6)((() => [(0, n.bF)(P, {
                                active: e.active,
                                "onUpdate:active": a[27] || (a[27] = a => e.active = a),
                                scrollspy: "",
                                sticky: "",
                                class: "sticky-tabs",
                                "offset-top": "44"
                            }, {
                                default: (0, n.k6)((() => [a[90] || (a[90] = (0, n.Lk)("div", {
                                    style: {
                                        height: "1rem"
                                    }
                                }, null, -1)), (0, n.bF)(U, {
                                    title: "升级"
                                }, {
                                    default: (0, n.k6)((() => [a[30] || (a[30] = (0, n.Lk)("hr", null, null, -1)), (0, n.bF)(g, {
                                        name: "智能策略",
                                        label: "智能策略:"
                                    }, {
                                        input: (0, n.k6)((() => [(0, n.Lk)("div", o, [a[28] || (a[28] = (0, n.Lk)("label", {
                                            for: "smart-strategy-switch",
                                            style: {
                                                "margin-right": "10px"
                                            }
                                        }, "全功能随主城等级自动调整 ", -1)), (0, n.bF)(V, {
                                            modelValue: h.配置.智能策略,
                                            "onUpdate:modelValue": a[0] || (a[0] = e => h.配置.智能策略 = e),
                                            size: "20px"
                                        }, null, 8, ["modelValue"])])])),
                                        _: 1
                                    }), h.配置.智能策略 ? ((0, n.uX)(), (0, n.Wv)(y, {
                                        key: 0,
                                        name: "脚本间隔",
                                        title: "运行一轮等待时间(分钟)"
                                    }, {
                                        default: (0, n.k6)((() => [(0, n.bF)(W, {
                                            modelValue: h.配置.脚本间隔,
                                            "onUpdate:modelValue": a[1] || (a[1] = e => h.配置.脚本间隔 = e),
                                            step: "5",
                                            min: "0",
                                            max: "60",
                                            theme: "round",
                                            "disable-input": ""
                                        }, null, 8, ["modelValue"])])),
                                        _: 1
                                    })) : (0, n.Q3)("", !0), h.配置.智能策略 ? ((0, n.uX)(), (0, n.Wv)(g, {
                                        key: 1,
                                        name: "智能策略说明",
                                        label: "说明:"
                                    }, {
                                        input: (0, n.k6)((() => a[29] || (a[29] = [(0, n.Lk)("div", {
                                            style: {
                                                display: "flex",
                                                "align-items": "center"
                                            }
                                        }, [(0, n.Lk)("label", {
                                            for: "smart-strategy-switch",
                                            style: {
                                                "margin-right": "10px",
                                                color: "#b0b0c0",
                                                "font-size": "14px"
                                            }
                                        }, [(0, n.eW)(" 1,智能策略支持0到16级,16级之前必须打开智能策略,会自动的过副本."), (0, n.Lk)("br"), (0, n.eW)("2,16级之后一定要改为自定义,因为智能策略会变笨. ")])], -1)]))),
                                        _: 1
                                    })) : (0, n.Q3)("", !0), h.配置.智能策略 ? (0, n.Q3)("", !0) : ((0, n.uX)(), (0, n.Wv)(y, {
                                        key: 2,
                                        name: "建筑加速",
                                        title: "使用加速道具:"
                                    }, {
                                        default: (0, n.k6)((() => [(0, n.bF)(V, {
                                            modelValue: h.配置.建筑加速,
                                            "onUpdate:modelValue": a[2] || (a[2] = e => h.配置.建筑加速 = e),
                                            size: "20px"
                                        }, null, 8, ["modelValue"])])),
                                        _: 1
                                    })), h.配置.智能策略 ? (0, n.Q3)("", !0) : ((0, n.uX)(), (0, n.Wv)(y, {
                                        key: 3,
                                        title: "升级建筑:"
                                    }, {
                                        default: (0, n.k6)((() => [((0, n.uX)(), (0, n.CE)(n.FK, null, (0, n.pI)(30, ((e, a) => (0, n.bo)((0, n.bF)(g, {
                                            key: a,
                                            modelValue: h.配置.选择建筑[`建筑${a}`],
                                            "onUpdate:modelValue": e => h.配置.选择建筑[`建筑${a}`] = e,
                                            laceholder: "请选择建筑",
                                            "is-link": "",
                                            readonly: "",
                                            placeholder: "点击选择建筑和等级",
                                            onClick: e => h.选择建筑click(`建筑${a}`)
                                        }, null, 8, ["modelValue", "onUpdate:modelValue", "onClick"]), [
                                            [t.aG, 0 == a | !h.isNullOrEmpty(h.配置.选择建筑[`建筑${a}`]) | !h.isNullOrEmpty(h.配置.选择建筑["建筑" + (a - 1)])]
                                        ]))), 64))])),
                                        _: 1
                                    })), (0, n.bF)(C, {
                                        show: h.showPicker_选择建筑,
                                        "onUpdate:show": a[3] || (a[3] = e => h.showPicker_选择建筑 = e),
                                        position: "bottom"
                                    }, {
                                        default: (0, n.k6)((() => [(0, n.bF)(w, {
                                            columns: h.选择建筑选项,
                                            onConfirm: h.onConfirm_选择建筑,
                                            onCancel: h.cancel_选择建筑
                                        }, null, 8, ["columns", "onConfirm", "onCancel"])])),
                                        _: 1
                                    }, 8, ["show"])])),
                                    _: 1
                                }), h.配置.智能策略 ? (0, n.Q3)("", !0) : ((0, n.uX)(), (0, n.CE)("div", i, [(0, n.bF)(U, {
                                    title: "科研"
                                }, {
                                    default: (0, n.k6)((() => [a[36] || (a[36] = (0, n.Lk)("hr", null, null, -1)), (0, n.bF)(g, {
                                        name: "科研项目",
                                        label: "科研项目:"
                                    }, {
                                        input: (0, n.k6)((() => [(0, n.bF)(O, {
                                            modelValue: h.配置.科研项目,
                                            "onUpdate:modelValue": a[4] || (a[4] = e => h.配置.科研项目 = e),
                                            direction: "horizontal",
                                            class: "checkbox-group"
                                        }, {
                                            default: (0, n.k6)((() => [(0, n.bF)(I, {
                                                name: "基础战斗",
                                                shape: "square"
                                            }, {
                                                default: (0, n.k6)((() => a[31] || (a[31] = [(0, n.eW)("基础战斗")]))),
                                                _: 1
                                            }), (0, n.bF)(I, {
                                                name: "城市发展",
                                                shape: "square"
                                            }, {
                                                default: (0, n.k6)((() => a[32] || (a[32] = [(0, n.eW)("城市发展")]))),
                                                _: 1
                                            }), (0, n.bF)(I, {
                                                name: "步兵训练",
                                                shape: "square"
                                            }, {
                                                default: (0, n.k6)((() => a[33] || (a[33] = [(0, n.eW)("步兵训练")]))),
                                                _: 1
                                            }), (0, n.bF)(I, {
                                                name: "射手训练",
                                                shape: "square"
                                            }, {
                                                default: (0, n.k6)((() => a[34] || (a[34] = [(0, n.eW)("射手训练")]))),
                                                _: 1
                                            }), (0, n.bF)(I, {
                                                name: "骑兵训练",
                                                shape: "square"
                                            }, {
                                                default: (0, n.k6)((() => a[35] || (a[35] = [(0, n.eW)("骑兵训练")]))),
                                                _: 1
                                            })])),
                                            _: 1
                                        }, 8, ["modelValue"])])),
                                        _: 1
                                    }), (0, n.bF)(g, {
                                        modelValue: h.配置.优先学习,
                                        "onUpdate:modelValue": a[5] || (a[5] = e => h.配置.优先学习 = e),
                                        "is-link": "",
                                        readonly: "",
                                        name: "优先学习",
                                        label: "优先研究 :",
                                        placeholder: "点击选择项目",
                                        onClick: a[6] || (a[6] = e => h.showPicker = !0)
                                    }, null, 8, ["modelValue"]), (0, n.bF)(C, {
                                        show: h.showPicker,
                                        "onUpdate:show": a[8] || (a[8] = e => h.showPicker = e),
                                        position: "bottom"
                                    }, {
                                        default: (0, n.k6)((() => [(0, n.bF)(w, {
                                            columns: h.优先学习columns,
                                            onConfirm: h.onConfirm,
                                            onCancel: a[7] || (a[7] = e => h.showPicker = !1)
                                        }, null, 8, ["columns", "onConfirm"])])),
                                        _: 1
                                    }, 8, ["show"])])),
                                    _: 1
                                }), (0, n.bF)(U, {
                                    title: "领奖"
                                }, {
                                    default: (0, n.k6)((() => [a[55] || (a[55] = (0, n.Lk)("hr", null, null, -1)), (0, n.bF)(g, {
                                        name: "领奖项目",
                                        label: "领奖项目:"
                                    }, {
                                        input: (0, n.k6)((() => [(0, n.bF)(O, {
                                            modelValue: h.配置.领奖项目,
                                            "onUpdate:modelValue": a[9] || (a[9] = e => h.配置.领奖项目 = e),
                                            class: "checkbox-group"
                                        }, {
                                            default: (0, n.k6)((() => [(0, n.bF)(I, {
                                                name: "背包开箱",
                                                shape: "square"
                                            }, {
                                                default: (0, n.k6)((() => a[37] || (a[37] = [(0, n.eW)("背包开钻")]))),
                                                _: 1
                                            }), (0, n.bF)(I, {
                                                name: "日常任务",
                                                shape: "square"
                                            }, {
                                                default: (0, n.k6)((() => a[38] || (a[38] = [(0, n.eW)("日常任务")]))),
                                                _: 1
                                            }), (0, n.bF)(I, {
                                                name: "活动中心",
                                                shape: "square"
                                            }, {
                                                default: (0, n.k6)((() => a[39] || (a[39] = [(0, n.eW)("活动中心")]))),
                                                _: 1
                                            }), (0, n.bF)(I, {
                                                name: "邮件奖励",
                                                shape: "square"
                                            }, {
                                                default: (0, n.k6)((() => a[40] || (a[40] = [(0, n.eW)("邮件奖励")]))),
                                                _: 1
                                            }), (0, n.bF)(I, {
                                                name: "免费招募",
                                                shape: "square"
                                            }, {
                                                default: (0, n.k6)((() => a[41] || (a[41] = [(0, n.eW)("免费招募")]))),
                                                _: 1
                                            }), (0, n.bF)(I, {
                                                name: "VIP领奖",
                                                shape: "square"
                                            }, {
                                                default: (0, n.k6)((() => a[42] || (a[42] = [(0, n.eW)("VIP领奖")]))),
                                                _: 1
                                            }), (0, n.bF)(I, {
                                                name: "联盟礼盒",
                                                shape: "square"
                                            }, {
                                                default: (0, n.k6)((() => a[43] || (a[43] = [(0, n.eW)("联盟礼盒")]))),
                                                _: 1
                                            }), (0, n.bF)(I, {
                                                name: "领土收益",
                                                shape: "square"
                                            }, {
                                                default: (0, n.k6)((() => a[44] || (a[44] = [(0, n.eW)("领土收益")]))),
                                                _: 1
                                            }), (0, n.bF)(I, {
                                                name: "竞技场",
                                                shape: "square"
                                            }, {
                                                default: (0, n.k6)((() => a[45] || (a[45] = [(0, n.eW)("竞技场")]))),
                                                _: 1
                                            }), (0, n.bF)(I, {
                                                name: "里程碑",
                                                shape: "square"
                                            }, {
                                                default: (0, n.k6)((() => a[46] || (a[46] = [(0, n.eW)("里程碑")]))),
                                                _: 1
                                            }), (0, n.bF)(I, {
                                                name: "改装车抽奖",
                                                shape: "square"
                                            }, {
                                                default: (0, n.k6)((() => a[47] || (a[47] = [(0, n.eW)("改装车抽奖")]))),
                                                _: 1
                                            }), (0, n.bF)(I, {
                                                name: "流浪商人",
                                                shape: "square"
                                            }, {
                                                default: (0, n.k6)((() => a[48] || (a[48] = [(0, n.eW)("流浪商人")]))),
                                                _: 1
                                            }), (0, n.bF)(I, {
                                                name: "家园保卫日志",
                                                shape: "square"
                                            }, {
                                                default: (0, n.k6)((() => a[49] || (a[49] = [(0, n.eW)("家园保卫日志")]))),
                                                _: 1
                                            })])),
                                            _: 1
                                        }, 8, ["modelValue"])])),
                                        _: 1
                                    }), (0, n.bF)(y, {
                                        name: "领奖间隔",
                                        title: "领奖间隔(小时)"
                                    }, {
                                        default: (0, n.k6)((() => [(0, n.bF)(W, {
                                            modelValue: h.配置.领奖间隔,
                                            "onUpdate:modelValue": a[10] || (a[10] = e => h.配置.领奖间隔 = e),
                                            step: "2",
                                            min: "2",
                                            max: "12",
                                            theme: "round",
                                            "disable-input": ""
                                        }, null, 8, ["modelValue"])])),
                                        _: 1
                                    }), h.配置.领奖项目.includes("流浪商人") ? ((0, n.uX)(), (0, n.Wv)(g, {
                                        key: 0,
                                        name: "流浪商人",
                                        label: "流浪商人需求:"
                                    }, {
                                        input: (0, n.k6)((() => [(0, n.bF)(O, {
                                            modelValue: h.配置.流浪商人,
                                            "onUpdate:modelValue": a[11] || (a[11] = e => h.配置.流浪商人 = e),
                                            direction: "horizontal",
                                            class: "checkbox-group",
                                            shape: "square"
                                        }, {
                                            default: (0, n.k6)((() => [(0, n.bF)(I, {
                                                name: "食物"
                                            }, {
                                                default: (0, n.k6)((() => a[50] || (a[50] = [(0, n.eW)("食物")]))),
                                                _: 1
                                            }), (0, n.bF)(I, {
                                                name: "水"
                                            }, {
                                                default: (0, n.k6)((() => a[51] || (a[51] = [(0, n.eW)("水")]))),
                                                _: 1
                                            }), (0, n.bF)(I, {
                                                name: "木材"
                                            }, {
                                                default: (0, n.k6)((() => a[52] || (a[52] = [(0, n.eW)("木材")]))),
                                                _: 1
                                            }), (0, n.bF)(I, {
                                                name: "肉"
                                            }, {
                                                default: (0, n.k6)((() => a[53] || (a[53] = [(0, n.eW)("肉")]))),
                                                _: 1
                                            }), (0, n.bF)(I, {
                                                name: "子弹"
                                            }, {
                                                default: (0, n.k6)((() => a[54] || (a[54] = [(0, n.eW)("子弹")]))),
                                                _: 1
                                            })])),
                                            _: 1
                                        }, 8, ["modelValue"])])),
                                        _: 1
                                    })) : (0, n.Q3)("", !0)])),
                                    _: 1
                                }), (0, n.bF)(U, {
                                    title: "采集"
                                }, {
                                    default: (0, n.k6)((() => [a[68] || (a[68] = (0, n.Lk)("hr", null, null, -1)), (0, n.bF)(g, {
                                        name: "采集队伍",
                                        label: "采集出征队伍"
                                    }, {
                                        input: (0, n.k6)((() => [(0, n.bF)(O, {
                                            modelValue: h.配置.采集队伍,
                                            "onUpdate:modelValue": a[12] || (a[12] = e => h.配置.采集队伍 = e),
                                            direction: "horizontal",
                                            class: "checkbox-group",
                                            shape: "square"
                                        }, {
                                            default: (0, n.k6)((() => [(0, n.bF)(I, {
                                                name: "1"
                                            }, {
                                                default: (0, n.k6)((() => a[56] || (a[56] = [(0, n.eW)("I队")]))),
                                                _: 1
                                            }), (0, n.bF)(I, {
                                                name: "2"
                                            }, {
                                                default: (0, n.k6)((() => a[57] || (a[57] = [(0, n.eW)("II队")]))),
                                                _: 1
                                            }), (0, n.bF)(I, {
                                                name: "3"
                                            }, {
                                                default: (0, n.k6)((() => a[58] || (a[58] = [(0, n.eW)("III队")]))),
                                                _: 1
                                            }), (0, n.bF)(I, {
                                                name: "4"
                                            }, {
                                                default: (0, n.k6)((() => a[59] || (a[59] = [(0, n.eW)("IV队")]))),
                                                _: 1
                                            })])),
                                            _: 1
                                        }, 8, ["modelValue"])])),
                                        _: 1
                                    }), (0, n.bF)(g, {
                                        name: "采集目标",
                                        label: "采集目标:"
                                    }, {
                                        input: (0, n.k6)((() => [(0, n.bF)(O, {
                                            modelValue: h.配置.采集目标,
                                            "onUpdate:modelValue": a[13] || (a[13] = e => h.配置.采集目标 = e),
                                            direction: "horizontal",
                                            class: "checkbox-group",
                                            shape: "square"
                                        }, {
                                            default: (0, n.k6)((() => [(0, n.bF)(I, {
                                                name: "食物"
                                            }, {
                                                default: (0, n.k6)((() => a[60] || (a[60] = [(0, n.eW)("食物")]))),
                                                _: 1
                                            }), (0, n.bF)(I, {
                                                name: "水"
                                            }, {
                                                default: (0, n.k6)((() => a[61] || (a[61] = [(0, n.eW)("水")]))),
                                                _: 1
                                            }), (0, n.bF)(I, {
                                                name: "木材"
                                            }, {
                                                default: (0, n.k6)((() => a[62] || (a[62] = [(0, n.eW)("木材")]))),
                                                _: 1
                                            }), (0, n.bF)(I, {
                                                name: "肉"
                                            }, {
                                                default: (0, n.k6)((() => a[63] || (a[63] = [(0, n.eW)("肉")]))),
                                                _: 1
                                            })])),
                                            _: 1
                                        }, 8, ["modelValue"])])),
                                        _: 1
                                    }), (0, n.bF)(g, {
                                        name: "驻扎目标",
                                        label: "驻扎目标:"
                                    }, {
                                        input: (0, n.k6)((() => [(0, n.bF)(O, {
                                            modelValue: h.配置.驻扎目标,
                                            "onUpdate:modelValue": a[14] || (a[14] = e => h.配置.驻扎目标 = e),
                                            direction: "horizontal",
                                            class: "checkbox-group",
                                            shape: "square"
                                        }, {
                                            default: (0, n.k6)((() => [(0, n.bF)(I, {
                                                name: "食物"
                                            }, {
                                                default: (0, n.k6)((() => a[64] || (a[64] = [(0, n.eW)("食物")]))),
                                                _: 1
                                            }), (0, n.bF)(I, {
                                                name: "水"
                                            }, {
                                                default: (0, n.k6)((() => a[65] || (a[65] = [(0, n.eW)("水")]))),
                                                _: 1
                                            }), (0, n.bF)(I, {
                                                name: "木材"
                                            }, {
                                                default: (0, n.k6)((() => a[66] || (a[66] = [(0, n.eW)("木材")]))),
                                                _: 1
                                            }), (0, n.bF)(I, {
                                                name: "肉"
                                            }, {
                                                default: (0, n.k6)((() => a[67] || (a[67] = [(0, n.eW)("肉")]))),
                                                _: 1
                                            })])),
                                            _: 1
                                        }, 8, ["modelValue"])])),
                                        _: 1
                                    }), (0, n.bF)(g, {
                                        name: "采集等级",
                                        label: "采集驻扎等级"
                                    }, {
                                        input: (0, n.k6)((() => [(0, n.bF)(W, {
                                            class: "stepper-container",
                                            modelValue: h.配置.采集等级,
                                            "onUpdate:modelValue": a[15] || (a[15] = e => h.配置.采集等级 = e),
                                            max: "25",
                                            min: "10",
                                            theme: "round",
                                            "disable-input": ""
                                        }, null, 8, ["modelValue"])])),
                                        _: 1
                                    })])),
                                    _: 1
                                }), (0, n.bF)(U, {
                                    title: "战斗"
                                }, {
                                    default: (0, n.k6)((() => [a[82] || (a[82] = (0, n.Lk)("hr", null, null, -1)), (0, n.bF)(y, {
                                        name: "收费招募",
                                        title: "招募券招募:"
                                    }, {
                                        default: (0, n.k6)((() => [(0, n.bF)(V, {
                                            modelValue: h.配置.收费招募,
                                            "onUpdate:modelValue": a[16] || (a[16] = e => h.配置.收费招募 = e),
                                            size: "20px"
                                        }, null, 8, ["modelValue"])])),
                                        _: 1
                                    }), (0, n.bF)(y, {
                                        name: "英雄升级",
                                        title: "英雄升级(慎选):"
                                    }, {
                                        label: (0, n.k6)((() => a[69] || (a[69] = [(0, n.Lk)("label", {
                                            style: {
                                                "margin-right": "10px"
                                            }
                                        }, "I队一直升,其他队25级 ", -1)]))),
                                        default: (0, n.k6)((() => [(0, n.bF)(V, {
                                            modelValue: h.配置.英雄升级,
                                            "onUpdate:modelValue": a[17] || (a[17] = e => h.配置.英雄升级 = e),
                                            size: "20px"
                                        }, null, 8, ["modelValue"])])),
                                        _: 1
                                    }), (0, n.bF)(g, {
                                        name: "训练骑兵",
                                        label: "训练骑兵等级"
                                    }, {
                                        input: (0, n.k6)((() => [(0, n.Lk)("div", d, [10 === h.配置.训练骑兵 ? ((0, n.uX)(), (0, n.CE)("span", s, "自识别最高级")) : (0, n.Q3)("", !0), 0 === h.配置.训练骑兵 ? ((0, n.uX)(), (0, n.CE)("span", r, "不训练")) : (0, n.Q3)("", !0), (0, n.bF)(W, {
                                            modelValue: h.配置.训练骑兵,
                                            "onUpdate:modelValue": a[18] || (a[18] = e => h.配置.训练骑兵 = e),
                                            max: "10",
                                            min: "0",
                                            theme: "round",
                                            "disable-input": ""
                                        }, null, 8, ["modelValue"])])])),
                                        _: 1
                                    }), (0, n.bF)(g, {
                                        name: "训练步兵",
                                        label: "训练步兵等级"
                                    }, {
                                        input: (0, n.k6)((() => [(0, n.Lk)("div", m, [10 === h.配置.训练步兵 ? ((0, n.uX)(), (0, n.CE)("span", c, "自识别最高级")) : (0, n.Q3)("", !0), 0 === h.配置.训练步兵 ? ((0, n.uX)(), (0, n.CE)("span", b, "不训练")) : (0, n.Q3)("", !0), (0, n.bF)(W, {
                                            modelValue: h.配置.训练步兵,
                                            "onUpdate:modelValue": a[19] || (a[19] = e => h.配置.训练步兵 = e),
                                            max: "10",
                                            min: "0",
                                            theme: "round",
                                            "disable-input": ""
                                        }, null, 8, ["modelValue"])])])),
                                        _: 1
                                    }), (0, n.bF)(g, {
                                        name: "训练弓兵",
                                        label: "训练弓兵等级"
                                    }, {
                                        input: (0, n.k6)((() => [(0, n.Lk)("div", p, [10 === h.配置.训练弓兵 ? ((0, n.uX)(), (0, n.CE)("span", k, "自识别最高级")) : (0, n.Q3)("", !0), 0 === h.配置.训练弓兵 ? ((0, n.uX)(), (0, n.CE)("span", f, "不训练")) : (0, n.Q3)("", !0), (0, n.bF)(W, {
                                            modelValue: h.配置.训练弓兵,
                                            "onUpdate:modelValue": a[20] || (a[20] = e => h.配置.训练弓兵 = e),
                                            max: "10",
                                            min: "0",
                                            theme: "round",
                                            "disable-input": ""
                                        }, null, 8, ["modelValue"])])])),
                                        _: 1
                                    }), (0, n.bF)(y, {
                                        name: "晋升士兵",
                                        title: "优先晋升士兵:"
                                    }, {
                                        default: (0, n.k6)((() => [(0, n.bF)(V, {
                                            modelValue: h.配置.晋升士兵,
                                            "onUpdate:modelValue": a[21] || (a[21] = e => h.配置.晋升士兵 = e),
                                            size: "20px"
                                        }, null, 8, ["modelValue"])])),
                                        _: 1
                                    }), (0, n.bF)(y, {
                                        title: "集结组队方式:"
                                    }, {
                                        label: (0, n.k6)((() => [(0, n.bF)(L, {
                                            modelValue: h.配置.集结方式,
                                            "onUpdate:modelValue": a[22] || (a[22] = e => h.配置.集结方式 = e),
                                            class: "radio-group"
                                        }, {
                                            default: (0, n.k6)((() => [(0, n.bF)(q, {
                                                name: "不集结"
                                            }, {
                                                default: (0, n.k6)((() => a[70] || (a[70] = [(0, n.eW)("不集结")]))),
                                                _: 1
                                            }), (0, n.bF)(q, {
                                                name: "混队"
                                            }, {
                                                default: (0, n.k6)((() => a[71] || (a[71] = [(0, n.eW)("混队")]))),
                                                _: 1
                                            }), (0, n.bF)(q, {
                                                name: "建队"
                                            }, {
                                                default: (0, n.k6)((() => a[72] || (a[72] = [(0, n.eW)("建队")]))),
                                                _: 1
                                            }), (0, n.bF)(q, {
                                                name: "先混再建"
                                            }, {
                                                default: (0, n.k6)((() => a[73] || (a[73] = [(0, n.eW)("先混再建")]))),
                                                _: 1
                                            })])),
                                            _: 1
                                        }, 8, ["modelValue"])])),
                                        _: 1
                                    }), "不集结" != h.配置.集结方式 ? ((0, n.uX)(), (0, n.Wv)(y, {
                                        key: 0,
                                        name: "集结队伍数量",
                                        title: "集结队伍数量:"
                                    }, {
                                        default: (0, n.k6)((() => [(0, n.bF)(W, {
                                            modelValue: h.配置.集结队伍数量,
                                            "onUpdate:modelValue": a[23] || (a[23] = e => h.配置.集结队伍数量 = e),
                                            min: "1",
                                            max: "4",
                                            theme: "round",
                                            "disable-input": ""
                                        }, null, 8, ["modelValue"])])),
                                        _: 1
                                    })) : (0, n.Q3)("", !0), "建队" === h.配置.集结方式 | "先混再建" === h.配置.集结方式 ? ((0, n.uX)(), (0, n.Wv)(y, {
                                        key: 1,
                                        title: "集结建队目标等级:"
                                    }, {
                                        label: (0, n.k6)((() => [(0, n.bF)(L, {
                                            modelValue: h.配置.集结等级,
                                            "onUpdate:modelValue": a[24] || (a[24] = e => h.配置.集结等级 = e),
                                            class: "radio-group"
                                        }, {
                                            default: (0, n.k6)((() => [(0, n.bF)(q, {
                                                name: "6"
                                            }, {
                                                default: (0, n.k6)((() => a[74] || (a[74] = [(0, n.eW)("6级")]))),
                                                _: 1
                                            }), (0, n.bF)(q, {
                                                name: "10"
                                            }, {
                                                default: (0, n.k6)((() => a[75] || (a[75] = [(0, n.eW)("10级")]))),
                                                _: 1
                                            }), (0, n.bF)(q, {
                                                name: "13"
                                            }, {
                                                default: (0, n.k6)((() => a[76] || (a[76] = [(0, n.eW)("13级")]))),
                                                _: 1
                                            }), (0, n.bF)(q, {
                                                name: "16"
                                            }, {
                                                default: (0, n.k6)((() => a[77] || (a[77] = [(0, n.eW)("16级")]))),
                                                _: 1
                                            }), (0, n.bF)(q, {
                                                name: "19"
                                            }, {
                                                default: (0, n.k6)((() => a[78] || (a[78] = [(0, n.eW)("19级")]))),
                                                _: 1
                                            }), (0, n.bF)(q, {
                                                name: "22"
                                            }, {
                                                default: (0, n.k6)((() => a[79] || (a[79] = [(0, n.eW)("22级")]))),
                                                _: 1
                                            }), (0, n.bF)(q, {
                                                name: "25"
                                            }, {
                                                default: (0, n.k6)((() => a[80] || (a[80] = [(0, n.eW)("25级")]))),
                                                _: 1
                                            }), (0, n.bF)(q, {
                                                name: "100"
                                            }, {
                                                default: (0, n.k6)((() => a[81] || (a[81] = [(0, n.eW)("依战力")]))),
                                                _: 1
                                            })])),
                                            _: 1
                                        }, 8, ["modelValue"])])),
                                        _: 1
                                    })) : (0, n.Q3)("", !0)])),
                                    _: 1
                                }), (0, n.bF)(U, {
                                    title: "其他"
                                }, {
                                    default: (0, n.k6)((() => [a[88] || (a[88] = (0, n.Lk)("hr", null, null, -1)), (0, n.bF)(y, {
                                        title: "联盟捐献:"
                                    }, {
                                        label: (0, n.k6)((() => [(0, n.bF)(L, {
                                            modelValue: h.配置.联盟捐献,
                                            "onUpdate:modelValue": a[25] || (a[25] = e => h.配置.联盟捐献 = e),
                                            class: "juanxian-group"
                                        }, {
                                            default: (0, n.k6)((() => [(0, n.bF)(q, {
                                                name: "0"
                                            }, {
                                                default: (0, n.k6)((() => a[83] || (a[83] = [(0, n.eW)("不捐")]))),
                                                _: 1
                                            }), (0, n.bF)(q, {
                                                name: "1"
                                            }, {
                                                default: (0, n.k6)((() => a[84] || (a[84] = [(0, n.eW)("随机")]))),
                                                _: 1
                                            }), (0, n.bF)(q, {
                                                name: "2"
                                            }, {
                                                default: (0, n.k6)((() => a[85] || (a[85] = [(0, n.eW)("发展")]))),
                                                _: 1
                                            }), (0, n.bF)(q, {
                                                name: "3"
                                            }, {
                                                default: (0, n.k6)((() => a[86] || (a[86] = [(0, n.eW)("战争")]))),
                                                _: 1
                                            }), (0, n.bF)(q, {
                                                name: "4"
                                            }, {
                                                default: (0, n.k6)((() => a[87] || (a[87] = [(0, n.eW)("辅助")]))),
                                                _: 1
                                            })])),
                                            _: 1
                                        }, 8, ["modelValue"])])),
                                        _: 1
                                    }), (0, n.bF)(y, {
                                        name: "脚本间隔",
                                        title: "运行一轮等待时间(分钟)"
                                    }, {
                                        default: (0, n.k6)((() => [(0, n.bF)(W, {
                                            modelValue: h.配置.脚本间隔,
                                            "onUpdate:modelValue": a[26] || (a[26] = e => h.配置.脚本间隔 = e),
                                            step: "5",
                                            min: "0",
                                            max: "60",
                                            theme: "round",
                                            "disable-input": ""
                                        }, null, 8, ["modelValue"])])),
                                        _: 1
                                    })])),
                                    _: 1
                                }), (0, n.bF)(U, {
                                    title: "说明"
                                }, {
                                    default: (0, n.k6)((() => [(0, n.bF)(y, {
                                        title: "QQ/微信:11975534 ",
                                        onClick: h.复制
                                    }, {
                                        label: (0, n.k6)((() => [(0, n.bF)(E, null, {
                                            default: (0, n.k6)((() => a[89] || (a[89] = [(0, n.eW)("点击复制")]))),
                                            _: 1
                                        })])),
                                        _: 1
                                    }, 8, ["onClick"])])),
                                    _: 1
                                })]))])),
                                _: 1
                            }, 8, ["active"]), (0, n.Lk)("div", v, [(0, n.bF)(Q, {
                                round: "",
                                block: "",
                                type: "primary",
                                "native-type": "submit"
                            }, {
                                default: (0, n.k6)((() => a[91] || (a[91] = [(0, n.eW)(" 确定 ")]))),
                                _: 1
                            })])])),
                            _: 1
                        }, 8, ["onSubmit"])]), a[92] || (a[92] = (0, n.Lk)("div", {
                            class: "content"
                        }, null, -1))])),
                        _: 1
                    })
                }
                l(4114);
                var x = l(3326),
                    F = l(953),
                    _ = {
                        data() {
                            return {
                                "升级": {
                                    username: ""
                                }
                            }
                        },
                        methods: {},
                        setup() {
                            const e = (0, F.Kh)({
                                    "智能策略": !1,
                                    "建筑加速": !1,
                                    "选择建筑": {},
                                    "科研项目": [],
                                    "优先学习": "",
                                    "领奖项目": [],
                                    "领奖间隔": "",
                                    "流浪商人": [],
                                    "采集队伍": [],
                                    "采集目标": [],
                                    "驻扎目标": [],
                                    "采集等级": "",
                                    "收费招募": !1,
                                    "英雄升级": !1,
                                    "训练骑兵": 10,
                                    "训练步兵": 10,
                                    "训练弓兵": 10,
                                    "晋升士兵": !1,
                                    "集结方式": "不集结",
                                    "集结队伍数量": "",
                                    "集结等级": "100",
                                    "联盟捐献": "不捐",
                                    "脚本间隔": ""
                                }),
                                a = [{
                                    text: "主城",
                                    value: "主城"
                                }, {
                                    text: "农田",
                                    value: "农田"
                                }, {
                                    text: "水井",
                                    value: "水井"
                                }, {
                                    text: "伐木场",
                                    value: "伐木场"
                                }, {
                                    text: "肉铺",
                                    value: "肉铺"
                                }, {
                                    text: "弹药库",
                                    value: "弹药库"
                                }, {
                                    text: "粮仓",
                                    value: "粮仓"
                                }, {
                                    text: "水塔",
                                    value: "水塔"
                                }, {
                                    text: "木材仓库",
                                    value: "木材仓库"
                                }, {
                                    text: "肉仓库",
                                    value: "肉仓库"
                                }, {
                                    text: "图书馆",
                                    value: "图书馆"
                                }, {
                                    text: "医院",
                                    value: "医院"
                                }, {
                                    text: "领地管理中心",
                                    value: "领地管理中心"
                                }, {
                                    text: "训练宿舍",
                                    value: "训练宿舍"
                                }, {
                                    text: "探索营地",
                                    value: "探索营地"
                                }, {
                                    text: "搏击场",
                                    value: "搏击场"
                                }, {
                                    text: "射击场",
                                    value: "射击场"
                                }, {
                                    text: "马场",
                                    value: "马场"
                                }, {
                                    text: "流浪商人",
                                    value: "流浪商人"
                                }, {
                                    text: "电台",
                                    value: "电台"
                                }, {
                                    text: "守卫塔",
                                    value: "守卫塔"
                                }, {
                                    text: "练兵场I",
                                    value: "练兵场I"
                                }, {
                                    text: "练兵场II",
                                    value: "练兵场II"
                                }, {
                                    text: "练兵场III",
                                    value: "练兵场III"
                                }, {
                                    text: "练兵场IV",
                                    value: "练兵场IV"
                                }, {
                                    text: "装备作坊",
                                    value: "装备作坊"
                                }, {
                                    text: "改装车工坊",
                                    value: "改装车工坊"
                                }, {
                                    text: "科学家实验室",
                                    value: "科学家实验室"
                                }, {
                                    text: "羊圈",
                                    value: "羊圈"
                                }, {
                                    text: "牛棚",
                                    value: "牛棚"
                                }, {
                                    text: "邮局",
                                    value: "邮局"
                                }, {
                                    text: "档案馆",
                                    value: "档案馆"
                                }, {
                                    text: "餐馆",
                                    value: "餐馆"
                                }, {
                                    text: "瞭望塔",
                                    value: "瞭望塔"
                                }, {
                                    text: "公告牌",
                                    value: "公告牌"
                                }, {
                                    text: "狗舍",
                                    value: "狗舍"
                                }, {
                                    text: "哨塔",
                                    value: "哨塔"
                                }],
                                l = [{
                                    text: "1级",
                                    value: "1"
                                }, {
                                    text: "2级",
                                    value: "2"
                                }, {
                                    text: "3级",
                                    value: "3"
                                }, {
                                    text: "4级",
                                    value: "4"
                                }, {
                                    text: "5级",
                                    value: "5"
                                }, {
                                    text: "6级",
                                    value: "6"
                                }, {
                                    text: "7级",
                                    value: "7"
                                }, {
                                    text: "8级",
                                    value: "8"
                                }, {
                                    text: "9级",
                                    value: "9"
                                }, {
                                    text: "10级",
                                    value: "10"
                                }, {
                                    text: "11级",
                                    value: "11"
                                }, {
                                    text: "12级",
                                    value: "12"
                                }, {
                                    text: "13级",
                                    value: "13"
                                }, {
                                    text: "14级",
                                    value: "14"
                                }, {
                                    text: "15级",
                                    value: "15"
                                }, {
                                    text: "16级",
                                    value: "16"
                                }, {
                                    text: "17级",
                                    value: "17"
                                }, {
                                    text: "18级",
                                    value: "18"
                                }, {
                                    text: "19级",
                                    value: "19"
                                }, {
                                    text: "20级",
                                    value: "20"
                                }, {
                                    text: "21级",
                                    value: "21"
                                }, {
                                    text: "22级",
                                    value: "22"
                                }, {
                                    text: "23级",
                                    value: "23"
                                }, {
                                    text: "24级",
                                    value: "24"
                                }, {
                                    text: "25级",
                                    value: "25"
                                }],
                                t = (0, F.Kh)([a, l]);

                            function n(e) {
                                return null === e || void 0 === e || "" === e
                            }
                            const u = [];
                            fetch(`${window.location.origin}/config.json`).then((e => e.json())).then((l => {
                                Object.assign(e, l);
                                for (const a in e.选择建筑) n(e.选择建筑[a]) && delete e.选择建筑[a], e.选择建筑[a] && u.push(e.选择建筑[a].split("---")[0]);
                                console.log(e.选择建筑), t[0] = a.filter((e => !u.includes(e.value)))
                            })).catch((e => {
                                console.error("Error fetching JSON data:", e)
                            }));
                            const o = (0, F.KR)(""),
                                i = e => {
                                    r.value = !0, o.value = e
                                },
                                d = () => {
                                    r.value = !1;
                                    const l = e.选择建筑[o.value]?.split("---")[0];
                                    if (l) {
                                        const e = u.indexOf(l); - 1 !== e && u.splice(e, 1), t[0] = a.filter((e => !u.includes(e.value)))
                                    }
                                    e.选择建筑[o.value] = null
                                },
                                s = ({
                                    selectedOptions: l
                                }) => {
                                    const n = e.选择建筑[o.value]?.split("---")[0];
                                    if (n) {
                                        const e = u.indexOf(n); - 1 !== e && u.splice(e, 1)
                                    }
                                    e.选择建筑[o.value] = `${l[0]?.value}---${l[1]?.value}级`, u.push(l[0]?.value), console.log(u), t[0] = a.filter((e => !u.includes(e.value))), r.value = !1
                                },
                                r = (0, F.KR)(!1),
                                m = (0, F.KR)(!1),
                                c = [{
                                    text: "选择项目",
                                    value: ""
                                }, {
                                    text: "协同作战1",
                                    value: "协同作战1"
                                }, {
                                    text: "协同作战2",
                                    value: "协同作战2"
                                }, {
                                    text: "协同作战3",
                                    value: "协同作战3"
                                }, {
                                    text: "协同作战4",
                                    value: "协同作战4"
                                }, {
                                    text: "协同作战5",
                                    value: "协同作战5"
                                }, {
                                    text: "协同作战6",
                                    value: "协同作战6"
                                }, {
                                    text: "协同作战7",
                                    value: "协同作战7"
                                }],
                                b = ({
                                    selectedOptions: a
                                }) => {
                                    e.优先学习 = a[0]?.text, m.value = !1
                                },
                                p = async () => {
                                    fetch(`${window.location.origin}/api/exit`).catch((e => {
                                        console.error("Error fetching JSON data:", e)
                                    }))
                                }, k = async () => {
                                    try {
                                        const e = {
                                                "Content-Type": "application/json"
                                            },
                                            a = `${window.location.origin}/api/copy`,
                                            l = await fetch(a, {
                                                method: "GET",
                                                headers: e
                                            });
                                        if (!l.ok) throw new Error(`HTTP error! status: ${l.status}`);
                                        (0, x.P0)("已复制到剪切板")
                                    } catch (e) {
                                        console.error("There was a problem with the fetch operation:", e)
                                    }
                                }, f = async () => {
                                    try {
                                        const a = JSON.stringify(e);
                                        console.log(a);
                                        const l = {
                                                "Content-Type": "application/json",
                                                "Content-Length": a.length
                                            },
                                            t = `${window.location.origin}/api/submit-form`,
                                            n = await fetch(t, {
                                                method: "POST",
                                                headers: l,
                                                body: a
                                            });
                                        if (!n.ok) throw new Error(`HTTP error! status: ${n.status}`);
                                        const u = n.headers.get("content-type");
                                        if (u && u.includes("application/json")) {
                                            const e = await n.json();
                                            console.log(e)
                                        } else {
                                            const e = await n.text();
                                            console.error("Server returned non-JSON response:", e)
                                        }
                                    } catch (a) {
                                        console.error("There was a problem with the fetch operation:", a)
                                    }
                                };
                            return {
                                "复制": k,
                                onExit: p,
                                "已选建筑": u,
                                isNullOrEmpty: n,
                                "配置": e,
                                "当前选择建筑": o,
                                "选择建筑click": i,
                                "onConfirm_选择建筑": s,
                                "showPicker_选择建筑": r,
                                "选择建筑选项": t,
                                "cancel_选择建筑": d,
                                "优先学习columns": c,
                                onConfirm: b,
                                showPicker: m,
                                onSubmit: f
                            }
                        }
                    },
                    V = l(6262);
                const g = (0, V.A)(_, [
                    ["render", h],
                    ["__scopeId", "data-v-e714f03a"]
                ]);
                var W = g,
                    y = l(3062),
                    w = (l(2241), (0, t.Ef)(W));
                w.use(y.Ay$), w.mount("#app")
            }
        },
        a = {};

    function l(t) {
        var n = a[t];
        if (void 0 !== n) return n.exports;
        var u = a[t] = {
            exports: {}
        };
        return e[t].call(u.exports, u, u.exports, l), u.exports
    }
    l.m = e,
        function () {
            var e = [];
            l.O = function (a, t, n, u) {
                if (!t) {
                    var o = 1 / 0;
                    for (r = 0; r < e.length; r++) {
                        t = e[r][0], n = e[r][1], u = e[r][2];
                        for (var i = !0, d = 0; d < t.length; d++)(!1 & u || o >= u) && Object.keys(l.O).every((function (e) {
                            return l.O[e](t[d])
                        })) ? t.splice(d--, 1) : (i = !1, u < o && (o = u));
                        if (i) {
                            e.splice(r--, 1);
                            var s = n();
                            void 0 !== s && (a = s)
                        }
                    }
                    return a
                }
                u = u || 0;
                for (var r = e.length; r > 0 && e[r - 1][2] > u; r--) e[r] = e[r - 1];
                e[r] = [t, n, u]
            }
        }(),
        function () {
            l.d = function (e, a) {
                for (var t in a) l.o(a, t) && !l.o(e, t) && Object.defineProperty(e, t, {
                    enumerable: !0,
                    get: a[t]
                })
            }
        }(),
        function () {
            l.g = function () {
                if ("object" === typeof globalThis) return globalThis;
                try {
                    return this || new Function("return this")()
                } catch (e) {
                    if ("object" === typeof window) return window
                }
            }()
        }(),
        function () {
            l.o = function (e, a) {
                return Object.prototype.hasOwnProperty.call(e, a)
            }
        }(),
        function () {
            var e = {
                524: 0
            };
            l.O.j = function (a) {
                return 0 === e[a]
            };
            var a = function (a, t) {
                    var n, u, o = t[0],
                        i = t[1],
                        d = t[2],
                        s = 0;
                    if (o.some((function (a) {
                            return 0 !== e[a]
                        }))) {
                        for (n in i) l.o(i, n) && (l.m[n] = i[n]);
                        if (d) var r = d(l)
                    }
                    for (a && a(t); s < o.length; s++) u = o[s], l.o(e, u) && e[u] && e[u][0](), e[u] = 0;
                    return l.O(r)
                },
                t = self["webpackChunkvue3_cli"] = self["webpackChunkvue3_cli"] || [];
            t.forEach(a.bind(null, 0)), t.push = a.bind(null, t.push.bind(t))
        }();
    var t = l.O(void 0, [504], (function () {
        return l(6862)
    }));
    t = l.O(t)
})();
-- 配置构建器模块
-- 用于简化前端界面配置的创建

local configBuilder = {}

-- 创建开关组件
function configBuilder.createSwitch(options)
  return {
    type = "switch",
    id = options.id or options.name,
    name = options.name,
    label = options.label or (options.name .. ":"),
    description = options.description,
    defaultValue = options.defaultValue or false,
    disabled = options.disabled or false,
    required = options.required or false,
    properties = {
      size = options.size or "normal",
      activeColor = options.activeColor,
      inactiveColor = options.inactiveColor
    }
  }
end

-- 创建步进器组件
function configBuilder.createStepper(options)
  return {
    type = "stepper",
    id = options.id or options.name,
    name = options.name,
    label = options.label or (options.name .. ":"),
    defaultValue = options.defaultValue or 0,
    disabled = options.disabled or false,
    required = options.required or false,
    properties = {
      min = options.min or 0,
      max = options.max or 100,
      step = options.step or 1,
      theme = options.theme or "round",
      disableInput = options.disableInput ~= false,
      showText = options.showText
    }
  }
end

-- 创建复选框组
function configBuilder.createCheckboxGroup(options)
  return {
    type = "checkbox-group",
    id = options.id or options.name,
    name = options.name,
    label = options.label or (options.name .. ":"),
    defaultValue = options.defaultValue or {},
    disabled = options.disabled or false,
    required = options.required or false,
    properties = {
      direction = options.direction or "horizontal",
      max = options.max or 0,
      shape = options.shape or "round",
      options = options.options or {},
      layout = {
        columns = options.columns or 2,
        gap = options.gap or "10px"
      }
    }
  }
end

-- 创建单选框组
function configBuilder.createRadioGroup(options)
  return {
    type = "radio-group",
    id = options.id or options.name,
    name = options.name,
    label = options.label or (options.name .. ":"),
    defaultValue = options.defaultValue or "",
    disabled = options.disabled or false,
    required = options.required or false,
    properties = {
      direction = options.direction or "horizontal",
      options = options.options or {},
      layout = {
        columns = options.columns or 4,
        gap = options.gap or "10px"
      }
    }
  }
end

-- 创建选择器组件
function configBuilder.createPicker(options)
  return {
    type = "picker",
    id = options.id or options.name,
    name = options.name,
    label = options.label or (options.name .. ":"),
    defaultValue = options.defaultValue or "",
    disabled = options.disabled or false,
    required = options.required or false,
    properties = {
      columns = options.columns or {},
      showToolbar = options.showToolbar ~= false,
      title = options.title or options.label,
      confirmButtonText = options.confirmButtonText or "确认",
      cancelButtonText = options.cancelButtonText or "取消"
    }
  }
end

-- 创建输入框组件
function configBuilder.createInput(options)
  return {
    type = "input",
    id = options.id or options.name,
    name = options.name,
    label = options.label or (options.name .. ":"),
    defaultValue = options.defaultValue or "",
    disabled = options.disabled or false,
    required = options.required or false,
    properties = {
      type = options.inputType or "text",
      placeholder = options.placeholder,
      maxlength = options.maxlength,
      readonly = options.readonly or false,
      clearable = options.clearable ~= false,
      showWordLimit = options.showWordLimit or false
    },
    validation = options.validation
  }
end

-- 创建文本组件
function configBuilder.createText(options)
  return {
    type = "text",
    id = options.id or options.name,
    name = options.name,
    label = options.label,
    content = options.content or "",
    properties = {
      size = options.size or "normal",
      color = options.color,
      align = options.align or "left",
      html = options.html or false
    },
    className = options.className
  }
end

-- 创建标签页布局
function configBuilder.createTabsLayout(tabs)
  return {
    type = "tabs",
    config = {
      sticky = true,
      swipeable = true,
      animated = true,
      tabs = tabs
    }
  }
end

-- 创建表单布局
function configBuilder.createFormLayout()
  return {
    type = "form"
  }
end

-- 创建完整页面配置
function configBuilder.createPageConfig(options)
  return {
    pageConfig = {
      title = options.title or "配置界面",
      theme = options.theme or "dark",
      layout = options.layout or configBuilder.createFormLayout(),
      components = options.components or {},
      validation = options.validation or {},
      actions = {
        submit = options.submitUrl or "/api/submit-form",
        exit = options.exitUrl or "/api/exit"
      }
    }
  }
end

-- 辅助函数：创建选项列表
function configBuilder.createOptions(optionList)
  local options = {}
  for i, option in ipairs(optionList) do
    if type(option) == "string" then
      table.insert(options, {value = option, label = option})
    else
      table.insert(options, option)
    end
  end
  return options
end

-- 辅助函数：创建选择器列
function configBuilder.createPickerColumns(valueList)
  local values = {}
  for i, item in ipairs(valueList) do
    if type(item) == "string" then
      table.insert(values, {text = item, value = item})
    else
      table.insert(values, item)
    end
  end
  return {{values = values}}
end

return configBuilder

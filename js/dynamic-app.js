// 动态配置界面应用
const { createApp, ref, reactive, computed, onMounted } = Vue;

// 注册Vant组件
const {
  NavBar, Form, Field, Switch, Stepper, CheckboxGroup, Checkbox,
  RadioGroup, Radio, Cell, Picker, Popup, Tabs, Tab, Button,
  ConfigProvider
} = vant;

// 创建Vue应用
const app = createApp({
  components: {
    [NavBar.name]: NavBar,
    [Form.name]: Form,
    [Field.name]: Field,
    [Switch.name]: Switch,
    [Stepper.name]: Stepper,
    [CheckboxGroup.name]: CheckboxGroup,
    [Checkbox.name]: Checkbox,
    [RadioGroup.name]: RadioGroup,
    [Radio.name]: Radio,
    [Cell.name]: Cell,
    [Picker.name]: Picker,
    [Popup.name]: Popup,
    [Tabs.name]: Tabs,
    [Tab.name]: Tab,
    [Button.name]: Button,
    [ConfigProvider.name]: ConfigProvider
  },
  
  setup() {
    // 页面配置数据
    const pageConfig = ref({
      title: '配置界面',
      theme: 'dark',
      layout: { type: 'form' },
      components: {},
      actions: {
        submit: '/api/submit-form',
        exit: '/api/exit'
      }
    });
    
    // 表单数据
    const formData = reactive({});
    
    // 表单引用
    const formRef = ref(null);
    
    // 活动标签页
    const activeTab = ref(0);
    
    // 选择器显示状态
    const pickerStates = reactive({});
    
    // 计算布局组件类型
    const isTabsLayout = computed(() => {
      return pageConfig.value.layout?.type === 'tabs';
    });
    
    // 获取标签页配置
    const tabs = computed(() => {
      return pageConfig.value.layout?.config?.tabs || [];
    });
    
    // 获取指定标签页的组件
    const getTabComponents = (tab) => {
      const tabComponents = {};
      if (tab.components && Array.isArray(tab.components)) {
        tab.components.forEach(componentId => {
          if (pageConfig.value.components[componentId]) {
            tabComponents[componentId] = pageConfig.value.components[componentId];
          }
        });
      }
      return tabComponents;
    };
    
    // 获取所有组件（用于表单布局）
    const getAllComponents = () => {
      return pageConfig.value.components || {};
    };
    
    // 更新表单数据
    const updateFormData = (name, value) => {
      formData[name] = value;
    };
    
    // 初始化表单数据
    const initFormData = () => {
      const components = pageConfig.value.components || {};
      Object.keys(components).forEach(key => {
        const component = components[key];
        if (component.defaultValue !== undefined) {
          formData[key] = component.defaultValue;
        }
      });
    };
    
    // 提交表单
    const onSubmit = async () => {
      try {
        const submitUrl = pageConfig.value.actions?.submit || '/api/submit-form';
        const response = await fetch(submitUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(formData)
        });
        
        if (response.ok) {
          console.log('配置提交成功');
        }
      } catch (error) {
        console.error('提交失败:', error);
      }
    };
    
    // 退出应用
    const onExit = async () => {
      try {
        const exitUrl = pageConfig.value.actions?.exit || '/api/exit';
        await fetch(exitUrl);
      } catch (error) {
        console.error('退出失败:', error);
      }
    };
    
    // 加载配置
    const loadConfig = async () => {
      try {
        const response = await fetch('/api/config');
        if (response.ok) {
          const config = await response.json();
          pageConfig.value = config.pageConfig || pageConfig.value;
          initFormData();
        }
      } catch (error) {
        console.error('加载配置失败:', error);
        initFormData();
      }
    };
    
    // 获取选择器显示值
    const getPickerDisplayValue = (componentId, config) => {
      const value = formData[componentId];
      if (!value) return '请选择';
      
      const firstColumn = config.properties?.columns?.[0];
      if (firstColumn && firstColumn.values) {
        const option = firstColumn.values.find(item => item.value === value);
        return option ? option.text : value;
      }
      
      return value;
    };
    
    // 选择器确认
    const onPickerConfirm = (componentId, { selectedOptions }) => {
      if (selectedOptions && selectedOptions.length > 0) {
        const selectedValue = selectedOptions[0].value;
        updateFormData(componentId, selectedValue);
      }
      pickerStates[componentId] = false;
    };
    
    // 显示选择器
    const showPicker = (componentId) => {
      pickerStates[componentId] = true;
    };
    
    // 获取步进器显示文本
    const getStepperText = (config, value) => {
      const showTextConfig = config.properties?.showText;
      if (showTextConfig && typeof showTextConfig === 'object') {
        return showTextConfig[value] || null;
      }
      return null;
    };
    
    // 组件挂载时加载配置
    onMounted(() => {
      loadConfig();
    });
    
    return {
      pageConfig,
      formData,
      formRef,
      activeTab,
      pickerStates,
      isTabsLayout,
      tabs,
      getTabComponents,
      getAllComponents,
      updateFormData,
      onSubmit,
      onExit,
      getPickerDisplayValue,
      onPickerConfirm,
      showPicker,
      getStepperText
    };
  },
  
  template: `
    <van-config-provider :theme="pageConfig.theme || 'dark'">
      <div class="form-container">
        <!-- 导航栏 -->
        <van-nav-bar
          :title="pageConfig.title || '配置界面'"
          left-text="退出"
          right-text="确定"
          @click-left="onExit"
          @click-right="onSubmit"
          class="fixed-nav-bar"
        />
        
        <!-- 主表单 -->
        <van-form ref="formRef" @submit="onSubmit">
          <!-- 标签页布局 -->
          <template v-if="isTabsLayout">
            <van-tabs 
              v-model:active="activeTab"
              :sticky="true"
              :swipeable="true"
              :animated="true"
              class="sticky-tabs"
            >
              <van-tab 
                v-for="tab in tabs" 
                :key="tab.name"
                :title="tab.title"
                :name="tab.name"
              >
                <div class="tab-content">
                  <template v-for="(component, componentId) in getTabComponents(tab)" :key="componentId">
                    <!-- 分隔线 -->
                    <hr v-if="component.showDivider !== false" />
                    
                    <!-- 动态渲染组件 -->
                    <component
                      :is="'dynamic-' + component.type"
                      :config="component"
                      :componentId="componentId"
                      :modelValue="formData[componentId]"
                      @update:modelValue="updateFormData(componentId, $event)"
                    />
                  </template>
                </div>
              </van-tab>
            </van-tabs>
          </template>
          
          <!-- 表单布局 -->
          <template v-else>
            <div style="padding: 16px;">
              <template v-for="(component, componentId) in getAllComponents()" :key="componentId">
                <!-- 分隔线 -->
                <hr v-if="component.showDivider !== false" />
                
                <!-- 动态渲染组件 -->
                <component
                  :is="'dynamic-' + component.type"
                  :config="component"
                  :componentId="componentId"
                  :modelValue="formData[componentId]"
                  @update:modelValue="updateFormData(componentId, $event)"
                />
              </template>
            </div>
          </template>
        </van-form>
      </div>
    </van-config-provider>
  `
});

// 注册动态组件
app.component('dynamic-switch', {
  props: ['config', 'componentId', 'modelValue'],
  emits: ['update:modelValue'],
  template: `
    <van-field
      :name="componentId"
      :label="config.label"
      :required="config.required"
    >
      <template #input>
        <div class="switch-container">
          <label
            v-if="config.description"
            :for="componentId + '-switch'"
            class="switch-description"
          >
            {{ config.description }}
          </label>
          <van-switch
            :id="componentId + '-switch'"
            :model-value="modelValue"
            @update:model-value="$emit('update:modelValue', $event)"
            :size="config.properties?.size || 'normal'"
            :active-color="config.properties?.activeColor"
            :inactive-color="config.properties?.inactiveColor"
            :disabled="config.disabled"
          />
        </div>
      </template>
    </van-field>
  `
});

app.component('dynamic-stepper', {
  props: ['config', 'componentId', 'modelValue'],
  emits: ['update:modelValue'],
  setup(props) {
    const showText = computed(() => {
      const showTextConfig = props.config.properties?.showText;
      if (showTextConfig && typeof showTextConfig === 'object') {
        return showTextConfig[props.modelValue] || null;
      }
      return null;
    });
    return { showText };
  },
  template: `
    <van-field
      :name="componentId"
      :label="config.label"
      :required="config.required"
    >
      <template #input>
        <div class="stepper-container">
          <span
            v-if="showText"
            class="stepper-text"
          >
            {{ showText }}
          </span>
          <van-stepper
            :model-value="modelValue"
            @update:model-value="$emit('update:modelValue', $event)"
            :min="config.properties?.min || 0"
            :max="config.properties?.max || 100"
            :step="config.properties?.step || 1"
            :theme="config.properties?.theme || 'round'"
            :disable-input="config.properties?.disableInput !== false"
            :disabled="config.disabled"
            class="stepper-component"
          />
        </div>
      </template>
    </van-field>
  `
});

app.component('dynamic-checkbox-group', {
  props: ['config', 'componentId', 'modelValue'],
  emits: ['update:modelValue'],
  setup(props) {
    const options = computed(() => props.config.properties?.options || []);
    const getGroupClass = () => {
      const layout = props.config.properties?.layout;
      if (layout?.columns) {
        return `checkbox-group-${layout.columns}-cols`;
      }
      return 'checkbox-group';
    };
    return { options, getGroupClass };
  },
  template: `
    <van-field
      :name="componentId"
      :label="config.label"
      :required="config.required"
    >
      <template #input>
        <van-checkbox-group
          :model-value="modelValue || []"
          @update:model-value="$emit('update:modelValue', $event)"
          :direction="config.properties?.direction || 'horizontal'"
          :max="config.properties?.max || 0"
          :disabled="config.disabled"
          :class="getGroupClass()"
        >
          <van-checkbox
            v-for="option in options"
            :key="option.value"
            :name="option.value"
            :shape="config.properties?.shape || 'round'"
            :disabled="option.disabled"
          >
            {{ option.label }}
          </van-checkbox>
        </van-checkbox-group>
      </template>
    </van-field>
  `
});

app.component('dynamic-radio-group', {
  props: ['config', 'componentId', 'modelValue'],
  emits: ['update:modelValue'],
  setup(props) {
    const options = computed(() => props.config.properties?.options || []);
    const getGroupClass = () => {
      const layout = props.config.properties?.layout;
      if (layout?.columns) {
        return `radio-group-${layout.columns}-cols`;
      }
      return 'radio-group';
    };
    return { options, getGroupClass };
  },
  template: `
    <van-cell
      :title="config.label"
      :required="config.required"
    >
      <template #label>
        <van-radio-group
          :model-value="modelValue"
          @update:model-value="$emit('update:modelValue', $event)"
          :direction="config.properties?.direction || 'horizontal'"
          :disabled="config.disabled"
          :class="getGroupClass()"
        >
          <van-radio
            v-for="option in options"
            :key="option.value"
            :name="option.value"
            :disabled="option.disabled"
          >
            {{ option.label }}
          </van-radio>
        </van-radio-group>
      </template>
    </van-cell>
  `
});

// 挂载应用
app.mount('#app');

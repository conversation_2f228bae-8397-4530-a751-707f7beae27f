<template>
  <div class="dynamic-tabs">
    <van-tabs 
      v-model:active="activeTab"
      :sticky="config.config?.sticky !== false"
      :swipeable="config.config?.swipeable !== false"
      :animated="config.config?.animated !== false"
      class="sticky-tabs"
    >
      <van-tab 
        v-for="tab in tabs" 
        :key="tab.name"
        :title="tab.title"
        :name="tab.name"
      >
        <div class="tab-content">
          <DynamicForm
            :components="getTabComponents(tab)"
            :formData="formData"
            @update="$emit('update', $event)"
          />
        </div>
      </van-tab>
    </van-tabs>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import DynamicForm from './DynamicForm.vue'

export default {
  name: 'DynamicTabs',
  components: {
    DynamicForm
  },
  props: {
    config: {
      type: Object,
      required: true
    },
    components: {
      type: Object,
      required: true
    },
    formData: {
      type: Object,
      required: true
    }
  },
  emits: ['update'],
  setup(props) {
    const activeTab = ref(0)
    
    // 获取标签页配置
    const tabs = computed(() => {
      return props.config.config?.tabs || []
    })
    
    // 获取指定标签页的组件
    const getTabComponents = (tab) => {
      const tabComponents = {}
      if (tab.components && Array.isArray(tab.components)) {
        tab.components.forEach(componentId => {
          if (props.components[componentId]) {
            tabComponents[componentId] = props.components[componentId]
          }
        })
      }
      return tabComponents
    }
    
    return {
      activeTab,
      tabs,
      getTabComponents
    }
  }
}
</script>

<style scoped>
.dynamic-tabs {
  width: 100%;
}

.sticky-tabs {
  position: sticky;
  top: 46px; /* 导航栏高度 */
  z-index: 999;
}

.tab-content {
  padding: 16px;
  min-height: calc(100vh - 138px); /* 减去导航栏和标签页高度 */
}
</style>

<template>
  <van-field 
    :name="componentId"
    :label="config.label"
    :required="config.required"
  >
    <template #input>
      <van-checkbox-group
        :model-value="modelValue || []"
        @update:model-value="$emit('update:modelValue', $event)"
        :direction="config.properties?.direction || 'horizontal'"
        :max="config.properties?.max || 0"
        :disabled="config.disabled"
        :class="getGroupClass()"
      >
        <van-checkbox
          v-for="option in options"
          :key="option.value"
          :name="option.value"
          :shape="config.properties?.shape || 'round'"
          :disabled="option.disabled"
        >
          {{ option.label }}
        </van-checkbox>
      </van-checkbox-group>
    </template>
  </van-field>
</template>

<script>
import { computed } from 'vue'

export default {
  name: 'DynamicCheckboxGroup',
  props: {
    config: {
      type: Object,
      required: true
    },
    componentId: {
      type: String,
      required: true
    },
    modelValue: {
      type: Array,
      default: () => []
    }
  },
  emits: ['update:modelValue'],
  setup(props) {
    // 获取选项列表
    const options = computed(() => {
      return props.config.properties?.options || []
    })
    
    // 获取组样式类
    const getGroupClass = () => {
      const layout = props.config.properties?.layout
      if (layout?.columns) {
        return `checkbox-group-${layout.columns}-cols`
      }
      return 'checkbox-group'
    }
    
    return {
      options,
      getGroupClass
    }
  }
}
</script>

<style scoped>
/* 默认复选框组样式 */
:deep(.checkbox-group) {
  display: grid;
  grid-template-columns: repeat(2, minmax(0, 1fr));
  gap: 10px 0;
}

/* 2列布局 */
:deep(.checkbox-group-2-cols) {
  display: grid;
  grid-template-columns: repeat(2, minmax(0, 1fr));
  gap: 10px 0;
}

/* 3列布局 */
:deep(.checkbox-group-3-cols) {
  display: grid;
  grid-template-columns: repeat(3, minmax(0, 1fr));
  gap: 10px 0;
}

/* 4列布局 */
:deep(.checkbox-group-4-cols) {
  display: grid;
  grid-template-columns: repeat(4, minmax(0, 1fr));
  gap: 10px 0;
}

/* 5列布局 */
:deep(.checkbox-group-5-cols) {
  display: grid;
  grid-template-columns: repeat(5, minmax(0, 1fr));
  gap: 10px 0;
}
</style>

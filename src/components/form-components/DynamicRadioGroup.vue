<template>
  <van-cell 
    :title="config.label"
    :required="config.required"
  >
    <template #label>
      <van-radio-group
        :model-value="modelValue"
        @update:model-value="$emit('update:modelValue', $event)"
        :direction="config.properties?.direction || 'horizontal'"
        :disabled="config.disabled"
        :class="getGroupClass()"
      >
        <van-radio
          v-for="option in options"
          :key="option.value"
          :name="option.value"
          :disabled="option.disabled"
        >
          {{ option.label }}
        </van-radio>
      </van-radio-group>
    </template>
  </van-cell>
</template>

<script>
import { computed } from 'vue'

export default {
  name: 'DynamicRadioGroup',
  props: {
    config: {
      type: Object,
      required: true
    },
    componentId: {
      type: String,
      required: true
    },
    modelValue: {
      type: [String, Number],
      default: ''
    }
  },
  emits: ['update:modelValue'],
  setup(props) {
    // 获取选项列表
    const options = computed(() => {
      return props.config.properties?.options || []
    })
    
    // 获取组样式类
    const getGroupClass = () => {
      const layout = props.config.properties?.layout
      if (layout?.columns) {
        return `radio-group-${layout.columns}-cols`
      }
      return 'radio-group'
    }
    
    return {
      options,
      getGroupClass
    }
  }
}
</script>

<style scoped>
/* 默认单选框组样式 */
:deep(.radio-group) {
  display: grid;
  grid-template-columns: repeat(4, minmax(0, 1fr));
  gap: 10px 0;
}

/* 2列布局 */
:deep(.radio-group-2-cols) {
  display: grid;
  grid-template-columns: repeat(2, minmax(0, 1fr));
  gap: 10px 0;
}

/* 3列布局 */
:deep(.radio-group-3-cols) {
  display: grid;
  grid-template-columns: repeat(3, minmax(0, 1fr));
  gap: 10px 0;
}

/* 4列布局 */
:deep(.radio-group-4-cols) {
  display: grid;
  grid-template-columns: repeat(4, minmax(0, 1fr));
  gap: 10px 0;
}

/* 5列布局 */
:deep(.radio-group-5-cols) {
  display: grid;
  grid-template-columns: repeat(5, minmax(0, 1fr));
  gap: 10px 0;
}
</style>

<template>
  <div class="dynamic-form">
    <template v-for="(component, componentId) in components" :key="componentId">
      <!-- 分隔线 -->
      <hr v-if="component.showDivider !== false" />
      
      <!-- 动态渲染组件 -->
      <component
        :is="getComponentType(component.type)"
        :config="component"
        :componentId="componentId"
        :modelValue="formData[componentId]"
        @update:modelValue="updateValue(componentId, $event)"
      />
    </template>
  </div>
</template>

<script>
import DynamicSwitch from './form-components/DynamicSwitch.vue'
import DynamicStepper from './form-components/DynamicStepper.vue'
import DynamicCheckboxGroup from './form-components/DynamicCheckboxGroup.vue'
import DynamicRadioGroup from './form-components/DynamicRadioGroup.vue'
import DynamicPicker from './form-components/DynamicPicker.vue'
import DynamicInput from './form-components/DynamicInput.vue'
import DynamicText from './form-components/DynamicText.vue'

export default {
  name: 'DynamicForm',
  components: {
    DynamicSwitch,
    DynamicStepper,
    DynamicCheckboxGroup,
    DynamicRadioGroup,
    DynamicPicker,
    DynamicInput,
    DynamicText
  },
  props: {
    components: {
      type: Object,
      required: true
    },
    formData: {
      type: Object,
      required: true
    }
  },
  emits: ['update'],
  setup(props, { emit }) {
    // 获取组件类型对应的Vue组件
    const getComponentType = (type) => {
      const componentMap = {
        'switch': 'DynamicSwitch',
        'stepper': 'DynamicStepper',
        'checkbox-group': 'DynamicCheckboxGroup',
        'radio-group': 'DynamicRadioGroup',
        'picker': 'DynamicPicker',
        'input': 'DynamicInput',
        'text': 'DynamicText'
      }
      return componentMap[type] || 'DynamicText'
    }
    
    // 更新表单值
    const updateValue = (componentId, value) => {
      emit('update', componentId, value)
    }
    
    return {
      getComponentType,
      updateValue
    }
  }
}
</script>

<style scoped>
.dynamic-form {
  width: 100%;
}

hr {
  border: none;
  border-top: 1px solid #ebedf0;
  margin: 16px 0;
}
</style>

-- 配置示例文件
-- 展示如何使用configBuilder创建各种界面配置

require("TSLib")
local configBuilder = require("configBuilder")
local json = require("ts").json

-- 示例1：简单表单配置
function createSimpleFormConfig()
  local components = {}
  
  -- 智能策略开关
  components.smart_strategy = configBuilder.createSwitch({
    name = "智能策略",
    label = "智能策略:",
    description = "全功能随主城等级自动调整",
    defaultValue = false
  })
  
  -- 脚本间隔步进器
  components.script_interval = configBuilder.createStepper({
    name = "脚本间隔",
    label = "脚本间隔(秒):",
    defaultValue = 5,
    min = 0,
    max = 60,
    step = 5,
    showText = {
      [0] = "不间隔",
      [60] = "最大间隔"
    }
  })
  
  -- 科研项目复选框
  components.research_projects = configBuilder.createCheckboxGroup({
    name = "科研项目",
    label = "科研项目:",
    defaultValue = {},
    columns = 2,
    options = configBuilder.createOptions({
      "基础战斗", "高级战斗", "防御研究", "经济发展"
    })
  })
  
  return configBuilder.createPageConfig({
    title = "简单配置示例",
    components = components
  })
end

-- 示例2：标签页配置
function createTabsConfig()
  local components = {}
  
  -- 基础设置标签页组件
  components.enable_auto = configBuilder.createSwitch({
    name = "自动模式",
    label = "启用自动模式:",
    defaultValue = true
  })
  
  components.check_interval = configBuilder.createStepper({
    name = "检查间隔",
    label = "检查间隔(分钟):",
    defaultValue = 5,
    min = 1,
    max = 30
  })
  
  -- 高级设置标签页组件
  components.battle_strategy = configBuilder.createRadioGroup({
    name = "战斗策略",
    label = "战斗策略:",
    defaultValue = "平衡",
    columns = 3,
    options = configBuilder.createOptions({
      "保守", "平衡", "激进"
    })
  })
  
  components.target_level = configBuilder.createPicker({
    name = "目标等级",
    label = "目标等级:",
    defaultValue = "5",
    columns = configBuilder.createPickerColumns({
      "1级", "2级", "3级", "4级", "5级"
    })
  })
  
  -- 其他设置标签页组件
  components.player_name = configBuilder.createInput({
    name = "玩家名称",
    label = "玩家名称:",
    placeholder = "请输入玩家名称",
    maxlength = 20,
    validation = {
      required = true,
      minLength = 2,
      messages = {
        required = "玩家名称不能为空",
        minLength = "玩家名称至少2个字符"
      }
    }
  })
  
  components.description = configBuilder.createText({
    name = "说明",
    label = "使用说明:",
    content = "这是一个示例配置界面，展示了各种组件的使用方法。",
    color = "primary",
    align = "center"
  })
  
  -- 创建标签页布局
  local layout = configBuilder.createTabsLayout({
    {
      name = "basic",
      title = "基础设置",
      components = {"enable_auto", "check_interval"}
    },
    {
      name = "advanced", 
      title = "高级设置",
      components = {"battle_strategy", "target_level"}
    },
    {
      name = "other",
      title = "其他设置", 
      components = {"player_name", "description"}
    }
  })
  
  return configBuilder.createPageConfig({
    title = "标签页配置示例",
    layout = layout,
    components = components
  })
end

-- 示例3：游戏配置（类似原项目）
function createGameConfig()
  local components = {}
  
  -- 智能策略
  components.smart_strategy = configBuilder.createSwitch({
    name = "智能策略",
    label = "智能策略:",
    description = "全功能随主城等级自动调整",
    defaultValue = false
  })
  
  -- 脚本间隔
  components.script_interval = configBuilder.createStepper({
    name = "脚本间隔",
    label = "脚本间隔(秒):",
    defaultValue = 5,
    min = 0,
    max = 60,
    step = 5
  })
  
  -- 科研项目
  components.research_projects = configBuilder.createCheckboxGroup({
    name = "科研项目",
    label = "科研项目:",
    defaultValue = {},
    columns = 2,
    options = configBuilder.createOptions({
      "基础战斗", "高级战斗", "防御研究", "经济发展", "军事工程", "医疗技术"
    })
  })
  
  -- 领奖项目
  components.reward_projects = configBuilder.createCheckboxGroup({
    name = "领奖项目", 
    label = "领奖项目:",
    defaultValue = {},
    columns = 2,
    options = configBuilder.createOptions({
      "背包开箱", "每日签到", "活动奖励", "流浪商人"
    })
  })
  
  -- 采集队伍
  components.gather_teams = configBuilder.createCheckboxGroup({
    name = "采集队伍",
    label = "采集出征队伍:",
    defaultValue = {},
    columns = 5,
    options = configBuilder.createOptions({"1", "2", "3", "4", "5"})
  })
  
  -- 集结方式
  components.rally_method = configBuilder.createRadioGroup({
    name = "集结方式",
    label = "集结组队方式:",
    defaultValue = "不集结",
    columns = 4,
    options = configBuilder.createOptions({
      "不集结", "混队", "建队", "先混再建"
    })
  })
  
  -- 创建标签页布局
  local layout = configBuilder.createTabsLayout({
    {
      name = "basic",
      title = "基础设置",
      components = {"smart_strategy", "script_interval"}
    },
    {
      name = "research",
      title = "科研设置", 
      components = {"research_projects"}
    },
    {
      name = "reward",
      title = "领奖设置",
      components = {"reward_projects"}
    },
    {
      name = "battle",
      title = "战斗设置",
      components = {"gather_teams", "rally_method"}
    }
  })
  
  return configBuilder.createPageConfig({
    title = "游戏配置界面",
    layout = layout,
    components = components
  })
end

-- 导出配置生成函数
return {
  createSimpleFormConfig = createSimpleFormConfig,
  createTabsConfig = createTabsConfig,
  createGameConfig = createGameConfig
}
